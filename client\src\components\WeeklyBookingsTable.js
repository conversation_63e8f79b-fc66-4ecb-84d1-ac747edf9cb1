import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Button,
  Chip,
  useTheme,
  alpha,
  CircularProgress,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Cancel as CancelIcon,
  AccessTime as AccessTimeIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  CheckCircle as CheckCircleIcon,
  SelfImprovement as RestIcon
} from '@mui/icons-material';
import { format, addDays } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import moment from 'moment-timezone';
import { formatDateInStudentTimezone } from '../utils/timezone';

const WeeklyBookingsTable = ({
  bookings,
  loading = false,
  currentWeekStart,
  daysOfWeek,
  onViewDetails,
  onCancelBooking,
  studentProfile,
  formatBookingTime,
  getStatusColor,
  isTeacherView = false,
  availableHours = null,
  onTakeBreak = null,
  weeklyBreaks = []
}) => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';

  // State for break dialog
  const [breakDialogOpen, setBreakDialogOpen] = React.useState(false);
  const [selectedBreakSlot, setSelectedBreakSlot] = React.useState(null);

  // Handle take break
  const handleTakeBreak = (day, timeSlot, isFirstHalf) => {
    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);
    const date = addDays(currentWeekStart, dayIndex);
    const slotMinute = isFirstHalf ? 0 : 30;

    setSelectedBreakSlot({
      day,
      date,
      hour: timeSlot.hour,
      minute: slotMinute,
      isFirstHalf,
      timeSlot
    });
    setBreakDialogOpen(true);
  };

  const confirmTakeBreak = () => {
    if (selectedBreakSlot && onTakeBreak) {
      onTakeBreak(selectedBreakSlot);
    }
    setBreakDialogOpen(false);
    setSelectedBreakSlot(null);
  };

  // Check if a half-hour slot is in the past (considering teacher's timezone)
  const isHalfHourSlotInPast = (day, timeSlot, isFirstHalf) => {
    try {
      const dayIndex = daysOfWeekData.findIndex(d => d.key === day);
      const date = addDays(currentWeekStart, dayIndex);

      const slotMinute = isFirstHalf ? 0 : 30;

      // Create the slot start datetime
      const slotStartTime = new Date(date);
      slotStartTime.setHours(timeSlot.hour, slotMinute, 0, 0);

      // Get current time in teacher's timezone using the same method as the table
      let currentTime;
      if (studentProfile && studentProfile.timezone) {
        // Use teacher's timezone
        const currentTimeStr = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
        currentTime = new Date(currentTimeStr);
      } else {
        // Fallback to browser local time
        currentTime = new Date();
      }

      // A slot is considered "past" if its start time has already passed
      // This means the current half-hour slot is also considered past
      return slotStartTime <= currentTime;

    } catch (error) {
      console.error('Error checking if slot is in past:', error);
      return false;
    }
  };

  // Check if a specific half-hour slot is available
  const isHalfHourSlotAvailable = (day, timeSlot, isFirstHalf) => {
    if (!availableHours || !isTeacherView) return false;

    const dayKey = day.toLowerCase();
    const daySlots = availableHours[dayKey];

    if (!daySlots || !Array.isArray(daySlots)) return false;

    // Check if this slot is taken as a break
    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);
    const date = addDays(currentWeekStart, dayIndex);
    const slotMinute = isFirstHalf ? 0 : 30;

    // Create break key in local time (frontend perspective)
    const breakKey = `${date.toISOString().split('T')[0]}_${timeSlot.hour.toString().padStart(2, '0')}:${slotMinute.toString().padStart(2, '0')}`;

    console.log('🔍 FRONTEND CHECK: Checking if slot is break');
    console.log('  📅 Date:', date.toISOString().split('T')[0]);
    console.log('  ⏰ Hour:', timeSlot.hour, 'Minute:', slotMinute);
    console.log('  🔑 Break key:', breakKey);
    console.log('  📋 All weekly breaks:', weeklyBreaks);
    console.log('  ❓ Is break?', weeklyBreaks.includes(breakKey));

    if (weeklyBreaks.includes(breakKey)) {
      console.log('  🚫 SLOT IS BREAK - hiding slot');
      return false; // Slot is taken as break
    }

    // Calculate the exact 30-minute slot we're checking (using existing slotMinute)
    const slotStartMinutes = timeSlot.hour * 60 + slotMinute;
    const slotEndMinutes = slotStartMinutes + 30;

    // Handle edge case for last half hour of the day (23:30-24:00)
    if (timeSlot.hour === 23 && !isFirstHalf) {
      // For 23:30-24:00, check if any available slot covers 23:30
      return daySlots.some(slot => {
        const [startTime, endTime] = slot.split('-');
        const [startHour, startMinute] = startTime.split(':').map(Number);
        let [endHour, endMinute] = endTime.split(':').map(Number);

        // Handle 24:00 or 00:00 as end time
        if (endHour === 0 || endHour === 24) {
          endHour = 24;
          endMinute = 0;
        }

        const availableStartMinutes = startHour * 60 + startMinute;
        const availableEndMinutes = endHour * 60 + endMinute;

        // Check if 23:30 is covered
        return slotStartMinutes >= availableStartMinutes &&
               slotStartMinutes < availableEndMinutes;
      });
    }

    // Check if this specific 30-minute slot is in the available hours
    return daySlots.some(slot => {
      const [startTime, endTime] = slot.split('-');
      const [startHour, startMinute] = startTime.split(':').map(Number);
      let [endHour, endMinute] = endTime.split(':').map(Number);

      // Handle 24:00 or 00:00 as end time (next day)
      if (endHour === 0) {
        endHour = 24;
        endMinute = 0;
      }

      const availableStartMinutes = startHour * 60 + startMinute;
      const availableEndMinutes = endHour * 60 + endMinute;

      // Check if the 30-minute slot fits within the available time range
      return slotStartMinutes >= availableStartMinutes && slotEndMinutes <= availableEndMinutes;
    });
  };

  // Define time slots - full hours from 00:00 to 23:00
  const timeSlots = [];
  for (let hour = 0; hour < 24; hour++) {
    const startTime = `${hour.toString().padStart(2, '0')}:00`;
    const midTime = `${hour.toString().padStart(2, '0')}:30`;
    const endTime = hour < 23 ? `${(hour + 1).toString().padStart(2, '0')}:00` : '00:00';
    timeSlots.push({
      key: `${startTime}-${endTime}`,
      label: startTime,
      midLabel: midTime,
      hour,
      minute: 0,
      // Include both half-hour slots for this hour
      firstHalf: `${startTime}-${midTime}`,
      secondHalf: hour < 23 ? `${midTime}-${endTime}` : '23:30-00:00'
    });
  }

  // Define days of the week
  const defaultDaysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const daysOfWeekData = daysOfWeek ? daysOfWeek.map(day => ({
    key: day,
    label: t(`days.${day}`)
  })) : defaultDaysOfWeek.map(day => ({
    key: day,
    label: t(`days.${day}`)
  }));

  // Get abbreviated day names for mobile
  const getAbbreviatedDayName = (dayKey) => {
    const abbreviations = {
      sunday: t('days.sundayShort') || 'Sun',
      monday: t('days.mondayShort') || 'Mon',
      tuesday: t('days.tuesdayShort') || 'Tue',
      wednesday: t('days.wednesdayShort') || 'Wed',
      thursday: t('days.thursdayShort') || 'Thu',
      friday: t('days.fridayShort') || 'Fri',
      saturday: t('days.saturdayShort') || 'Sat'
    };
    return abbreviations[dayKey] || dayKey.substring(0, 3);
  };

  // Get meeting status based on current time
  const getMeetingStatus = (booking) => {
    const meetingStartTime = new Date(booking.datetime);
    const meetingEndTime = new Date(booking.datetime);
    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));
    const now = new Date();

    if (booking.status === 'cancelled') {
      return 'cancelled';
    }

    if (now >= meetingStartTime && now < meetingEndTime) {
      return 'ongoing';
    }

    if (now >= meetingEndTime) {
      return 'completed';
    }

    return 'scheduled';
  };

  // Get background color based on meeting status
  const getBookingBackgroundColor = (booking) => {
    const status = getMeetingStatus(booking);

    switch (status) {
      case 'ongoing':
        return {
          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.85)} 0%, ${alpha(theme.palette.success.dark, 0.95)} 100%)`,
          border: `2px solid ${alpha(theme.palette.success.main, 0.3)}`,
          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.95)} 0%, ${alpha(theme.palette.success.dark, 1)} 100%)`
        };
      case 'completed':
        return {
          background: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.85)} 0%, ${alpha(theme.palette.grey[700], 0.95)} 100%)`,
          border: `2px solid ${alpha(theme.palette.grey[500], 0.3)}`,
          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.grey[500], 0.95)} 0%, ${alpha(theme.palette.grey[700], 1)} 100%)`
        };
      case 'cancelled':
        return {
          background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.85)} 0%, ${alpha(theme.palette.error.dark, 0.95)} 100%)`,
          border: `2px solid ${alpha(theme.palette.error.main, 0.3)}`,
          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.95)} 0%, ${alpha(theme.palette.error.dark, 1)} 100%)`
        };
      default: // scheduled
        return {
          background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.85)} 0%, ${alpha(theme.palette.info.dark, 0.95)} 100%)`,
          border: `2px solid ${alpha(theme.palette.info.main, 0.3)}`,
          hoverBackground: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.95)} 0%, ${alpha(theme.palette.info.dark, 1)} 100%)`
        };
    }
  };

  // Find all bookings for specific day and time slot
  const findBookingsForSlot = (day, timeSlot) => {
    if (!bookings || !currentWeekStart) return [];

    const dayIndex = daysOfWeekData.findIndex(d => d.key === day);
    const date = addDays(currentWeekStart, dayIndex);
    const dateStr = format(date, 'yyyy-MM-dd');

    return bookings.filter(booking => {
      // Get booking date and time in student timezone
      let bookingDate, bookingHour, bookingMinute;

      if (studentProfile && studentProfile.timezone) {
        // Use student timezone for both date and time
        const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
        const [datePart, timePart] = formattedDateTime.split(' ');
        bookingDate = datePart;

        const [hourStr, minuteStr] = timePart.split(':');
        bookingHour = parseInt(hourStr);
        bookingMinute = parseInt(minuteStr);
      } else {
        // Fallback to browser local time
        const bookingDateTime = new Date(booking.datetime);
        bookingDate = format(bookingDateTime, 'yyyy-MM-dd');
        bookingHour = bookingDateTime.getHours();
        bookingMinute = bookingDateTime.getMinutes();
      }

      // Check if booking matches this date
      const isDateMatch = bookingDate === dateStr;
      if (!isDateMatch) return false;

      // Calculate booking start and end times in minutes from midnight
      const duration = parseInt(booking.duration) || 25;
      const bookingStartMinutes = bookingHour * 60 + bookingMinute;
      const bookingEndMinutes = bookingStartMinutes + duration;

      // Calculate slot start and end times in minutes from midnight
      const slotStartMinutes = timeSlot.hour * 60;
      const slotEndMinutes = slotStartMinutes + 60;

      // For 50-minute lessons, show in both starting hour and next hour if it spans
      if (duration === 50) {
        // Check if booking starts in this slot
        const startsInThisSlot = bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotEndMinutes;

        // Check if booking extends into this slot from previous hour
        const extendsIntoThisSlot = bookingStartMinutes < slotStartMinutes && bookingEndMinutes > slotStartMinutes;

        return startsInThisSlot || extendsIntoThisSlot;
      } else {
        // For 25-minute lessons, show in the exact 30-minute slot
        const slotMiddle = slotStartMinutes + 30;

        // Check if booking is in first half (00-30) or second half (30-60)
        if (bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle) {
          // First half booking
          return bookingStartMinutes >= slotStartMinutes && bookingStartMinutes < slotMiddle;
        } else if (bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes) {
          // Second half booking
          return bookingStartMinutes >= slotMiddle && bookingStartMinutes < slotEndMinutes;
        }
      }

      return false;
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!bookings || bookings.length === 0) {
    return (
      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          {t('bookings.noBookings')}
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper elevation={3} sx={{ mb: 4, borderRadius: 2 }}>
      {/* Header */}
      <Box sx={{
        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
        p: 3,
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 2
      }}>
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
            📅 {t('bookings.weeklyTitle')}
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            {t('bookings.weeklyDescription')}
          </Typography>
        </Box>
      </Box>

      {/* Calendar Table */}
      <Box sx={{ overflow: 'auto' }}>
        {/* Days Header */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '60px repeat(7, minmax(80px, 1fr))',
            sm: '80px repeat(7, minmax(100px, 1fr))',
            md: '120px repeat(7, minmax(120px, 1fr))',
          },
          bgcolor: alpha(theme.palette.primary.main, 0.05),
          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          position: 'sticky',
          top: 0,
          zIndex: 10
        }}>
          <Box sx={{
            p: { xs: 1.5, sm: 2, md: 2.5 },
            minHeight: { xs: '60px', sm: '75px', md: '90px' },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
          }}>
            <Typography variant="body2" sx={{
              fontWeight: 'bold',
              color: theme.palette.text.secondary,
              fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' }
            }}>
              ⏰ {t('teacher.time')}
            </Typography>
          </Box>
          {daysOfWeekData.map((day, index) => {
            // Calculate the date for this day
            const dayDate = currentWeekStart ? addDays(currentWeekStart, index) : new Date();
            
            return (
              <Box
                key={day.key}
                sx={{
                  p: { xs: 1.5, sm: 2, md: 2.5 },
                  minHeight: { xs: '60px', sm: '75px', md: '90px' },
                  textAlign: 'center',
                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  '&:last-child': { borderRight: 'none' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <Typography variant="h6" sx={{
                  fontWeight: 'bold',
                  color: theme.palette.primary.main,
                  fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' },
                  lineHeight: 1.2
                }}>
                  {/* Day name */}
                  <Box component="span" sx={{ display: { xs: 'none', md: 'block' } }}>
                    {day.label}
                  </Box>
                  <Box component="span" sx={{ display: { xs: 'none', sm: 'block', md: 'none' } }}>
                    {day.label.length > 6 ? day.label.substring(0, 6) : day.label}
                  </Box>
                  <Box component="span" sx={{ display: { xs: 'block', sm: 'none' } }}>
                    {getAbbreviatedDayName(day.key)}
                  </Box>
                </Typography>
                
                {/* Date */}
                <Typography variant="caption" sx={{
                  display: 'block',
                  color: theme.palette.text.secondary,
                  fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' },
                  mt: 0.5
                }}>
                  {format(dayDate, 'MMM d', { locale: isRtl ? ar : enUS })}
                </Typography>
              </Box>
            );
          })}
        </Box>

        {/* Time Slots Grid */}
        <Box>
          {timeSlots.map((timeSlot, index) => {
            const isHourStart = timeSlot.minute === 0;
            return (
              <Box
                key={timeSlot.key}
                sx={{
                  display: 'grid',
                  gridTemplateColumns: {
                    xs: '60px repeat(7, minmax(80px, 1fr))',
                    sm: '80px repeat(7, minmax(100px, 1fr))',
                    md: '120px repeat(7, minmax(120px, 1fr))',
                  },
                  borderBottom: `1px solid ${alpha(theme.palette.divider, isHourStart ? 0.3 : 0.1)}`,
                  bgcolor: index % 4 < 2 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                {/* Time Labels */}
                <Box sx={{
                  p: { xs: 0.5, sm: 1, md: 1.5 },
                  display: 'grid',
                  placeItems: 'center',
                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  bgcolor: isHourStart ? alpha(theme.palette.primary.main, 0.05) : 'transparent',
                  position: 'relative'
                }}>
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column', 
                    alignItems: 'center', 
                    gap: 0.5,
                    position: 'absolute',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    top: timeSlot.minute === 0 ? '-8px' : 'calc(50% + 20px)'
                  }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 'bold',
                        color: theme.palette.primary.main,
                        fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' }
                      }}
                    >
                      {timeSlot.label}
                    </Typography>
                  </Box>
                </Box>

                {/* Day Cells */}
                {daysOfWeekData.map((day) => {
                  const allBookingsInSlot = findBookingsForSlot(day.key, timeSlot);

                  // Filter out extended bookings from previous hour
                  // Filter out extended bookings from previous hour
          const bookingsInSlot = allBookingsInSlot.filter(booking => {
                    // Get booking start time to determine if it's extended from previous hour
                    let bookingHour = 0;
                    let bookingMinute = 0;

                    if (studentProfile && studentProfile.timezone) {
                      const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
                      const [, timePart] = formattedDateTime.split(' ');
                      const [hourStr, minuteStr] = timePart.split(':');
                      bookingHour = parseInt(hourStr);
                      bookingMinute = parseInt(minuteStr);
                    } else {
                      const bookingDate = new Date(booking.datetime);
                      bookingHour = bookingDate.getHours();
                      bookingMinute = bookingDate.getMinutes();
                    }

                    const duration = parseInt(booking.duration) || 25;
                    const bookingStartMinutes = bookingHour * 60 + bookingMinute;
                    const currentSlotStartMinutes = timeSlot.hour * 60;

                    // Only show bookings that start in this slot or earlier in the same hour
                    // Don't show bookings that extend from previous hour
                    if (duration === 50) {
                      const extendsIntoThisSlot = bookingStartMinutes < currentSlotStartMinutes;
                      return !extendsIntoThisSlot; // Filter out extended bookings
                    }

                    return true; // Show all 25-minute bookings
                  });

                  // Deduplicate overlapping bookings that share the exact same start time (datetime)
                  // If a non-cancelled booking and a cancelled booking overlap, keep the non-cancelled one
                  // so the student sees the active reservation instead of the cancelled one.
                  const visibleBookingsMap = bookingsInSlot.reduce((acc, curr) => {
                    const key = curr.datetime; // ISO string coming from backend
                    const existing = acc[key];

                    if (!existing) {
                      acc[key] = curr; // first occurrence
                    } else {
                      // If the existing booking is cancelled and the new one is not, replace it
                      if (existing.status === 'cancelled' && curr.status !== 'cancelled') {
                        acc[key] = curr;
                      }
                      // If both have same non-cancelled status keep the earlier inserted one
                      // otherwise, leave as is.
                    }
                    return acc;
                  }, {});

                  const visibleBookings = Object.values(visibleBookingsMap);

                  return (
                    <Box
                      key={`${day.key}-${timeSlot.key}`}
                      sx={{
                        p: { xs: 0.2, sm: 0.4, md: 0.6 },
                        minHeight: { xs: '80px', sm: '100px', md: '120px' }, // Increased height for better content fit
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        '&:last-child': { borderRight: 'none' },
                        position: 'relative'
                      }}
                    >
                      {/* Continuous hour line */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          height: '4px',
                          bgcolor: theme.palette.primary.main,
                          zIndex: 0,
                          borderRadius: '2px',
                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                          pointerEvents: 'none'
                        }}
                      />
                      {/* Lighter half-hour line */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: 0,
                          right: 0,
                          height: '2px',
                          bgcolor: alpha(theme.palette.divider, 0.3), // More transparent
                          zIndex: 0, // Lower z-index to stay behind bookings
                          borderRadius: '1px'
                        }}
                      />

                      {visibleBookings.length > 0 ? (
                        <>
                          {visibleBookings.map((booking, bookingIndex) => {
                            const bookingColors = getBookingBackgroundColor(booking);
                            const isFullLesson = parseInt(booking.duration) === 50;

                            // Get booking start time to determine position
                            let bookingMinute = 0;
                            let bookingHour = 0;
                            if (studentProfile && studentProfile.timezone) {
                              const formattedDateTime = formatDateInStudentTimezone(booking.datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
                              const [, timePart] = formattedDateTime.split(' ');
                              const [hourStr, minuteStr] = timePart.split(':');
                              bookingHour = parseInt(hourStr);
                              bookingMinute = parseInt(minuteStr);
                            } else {
                              const bookingDate = new Date(booking.datetime);
                              bookingHour = bookingDate.getHours();
                              bookingMinute = bookingDate.getMinutes();
                            }

                            // For full lessons, determine which half of this slot the booking occupies
                            // For half lessons, show in correct half based on start time
                            let isFirstHalf = bookingMinute === 0;

                            // For full lessons, determine the visual representation
                            if (isFullLesson) {
                              // Calculate if this booking spans into the next hour
                              const bookingStartMinutes = bookingHour * 60 + bookingMinute;
                              const bookingEndMinutes = bookingStartMinutes + 50; // 50-minute lesson
                              const currentSlotStartMinutes = timeSlot.hour * 60;
                              const currentSlotEndMinutes = currentSlotStartMinutes + 60;

                              // Check if booking starts in this slot
                              const startsInThisSlot = bookingStartMinutes >= currentSlotStartMinutes && bookingStartMinutes < currentSlotEndMinutes;

                              if (startsInThisSlot) {
                                // Booking starts in this slot
                                const extendsToNextHour = bookingEndMinutes > currentSlotEndMinutes;
                                if (extendsToNextHour) {
                                  // This is a cross-hour booking, show it spanning from current position to next hour
                                  isFirstHalf = 'spanning'; // Special case for spanning bookings
                                } else {
                                  // Regular full lesson within one hour - don't override first half bookings
                                  isFirstHalf = bookingMinute === 0 ? null : 'secondHalfFull';
                                }
                              }
                            }

                            return (
                              <Box
                                key={`${booking.id}-${bookingIndex}`}
                                sx={{
                                  width: '90%',
                                  height: (() => {
                                    if (isFirstHalf === 'spanning') {
                                      // For cross-hour bookings, extend from second half to next hour
                                      return '100%'; // Extend to cover both halves visually
                                    }
                                    if (isFirstHalf === 'secondHalfFull') {
                                      // Full lesson starting from second half - only show in second half
                                      return '40%';
                                    }

                                    return isFullLesson ? '95%' : '40%';
                                  })(),
                                  position: 'absolute',
                                  top: (() => {
                                    if (isFirstHalf === 'spanning') {
                                      // Start from the second half of current slot
                                      return '50%';
                                    }
                                    if (isFirstHalf === 'secondHalfFull') {
                                      // Position in second half only
                                      return '55%';
                                    }

                                    return isFullLesson ? '2.5%' : (isFirstHalf ? '5%' : '55%');
                                  })(),
                                  left: '5%',
                                  zIndex: (() => {
                                    if (isFirstHalf === 'spanning') {
                                      return 100; // Very high z-index for spanning bookings
                                    }

                                    return 10 + bookingIndex; // Stacked z-index for multiple bookings
                                  })(),
                                  borderRadius: 1,
                                  background: bookingColors.background,
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  gap: { xs: 0.2, sm: 0.3, md: 0.4 },
                                  p: { xs: 0.2, sm: 0.3, md: 0.4 },
                                  cursor: 'pointer',
                                  border: bookingColors.border,
                                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                  '&:hover': {
                                    background: bookingColors.hoverBackground,
                                    transform: 'translateY(-1px)',
                                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                                  },
                                  transition: 'all 0.2s ease-in-out',
                                  overflow: 'hidden'
                                }}
                                onClick={() => onViewDetails(booking)}
                              >


                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: 'white',
                                    fontWeight: 'bold',
                                    fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.75rem' },
                                    textAlign: 'center',
                                    lineHeight: 1.1,
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: '-webkit-box',
                                    WebkitLineClamp: 1,
                                    WebkitBoxOrient: 'vertical',
                                    maxWidth: '100%',
                                    textShadow: '0 1px 2px rgba(0,0,0,0.3)',
                                    mb: { xs: 0.1, sm: 0.2, md: 0.2 }
                                  }}
                                >
                                  {isTeacherView ? booking.student_name : booking.teacher_name}
                                </Typography>

                                <Box sx={{ display: 'flex', gap: { xs: 0.2, sm: 0.3, md: 0.3 } }}>
                                  <Button
                                    size="small"
                                    variant="contained"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onViewDetails(booking);
                                    }}
                                    sx={{
                                      minWidth: 'auto',
                                      width: { xs: 18, sm: 22, md: 24 },
                                      height: { xs: 16, sm: 20, md: 22 },
                                      p: 0,
                                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                                      color: 'white',
                                      borderRadius: 0.5,
                                      '&:hover': {
                                        bgcolor: 'rgba(255, 255, 255, 0.3)',
                                      }
                                    }}
                                  >
                                    <ViewIcon sx={{ fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' } }} />
                                  </Button>
                                  {booking.status === 'scheduled' && onCancelBooking && !isTeacherView && (
                                    <Button
                                      size="small"
                                      variant="contained"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        onCancelBooking(booking);
                                      }}
                                      sx={{
                                        minWidth: 'auto',
                                        width: { xs: 18, sm: 22, md: 24 },
                                        height: { xs: 16, sm: 20, md: 22 },
                                        p: 0,
                                        bgcolor: 'rgba(244, 67, 54, 0.8)',
                                        color: 'white',
                                        borderRadius: 0.5,
                                        '&:hover': {
                                          bgcolor: 'rgba(244, 67, 54, 1)',
                                        }
                                      }}
                                    >
                                      <CancelIcon sx={{ fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' } }} />
                                    </Button>
                                  )}
                                </Box>
                              </Box>
                            );
                          })}
                        </>
                      ) : (
                        // Empty slot - show two halves for 30-minute slots
                        <Box sx={{
                          width: '90%',
                          height: { xs: '65px', sm: '85px', md: '105px' },
                          borderRadius: 1.5,
                          border: `1px solid ${alpha(theme.palette.grey[300], 0.5)}`,
                          display: 'flex',
                          flexDirection: 'column',
                          overflow: 'hidden',
                          zIndex: 1
                        }}>
                          {/* First Half (00-30 minutes) */}
                          {(() => {
                            const firstHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, true);
                            const firstHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, true);

                            return (
                              <Box sx={{
                                flex: 1,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderBottom: `1px solid ${alpha(theme.palette.grey[300], 0.3)}`,
                                bgcolor: firstHalfInPast
                                  ? alpha(theme.palette.grey[600], 0.15)
                                  : firstHalfAvailable
                                    ? alpha(theme.palette.success.main, 0.08)
                                    : alpha(theme.palette.grey[100], 0.3),
                                transition: 'all 0.2s ease'
                              }}>
                                {firstHalfInPast ? (
                                  <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.pastSlot', 'Past Time')}`} arrow>
                                    <RadioButtonUncheckedIcon
                                      sx={{
                                        color: theme.palette.grey[600],
                                        fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }
                                      }}
                                    />
                                  </Tooltip>
                                ) : firstHalfAvailable ? (
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                    <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:00 - ${t('bookings.availableSlot', 'Available')}`} arrow>
                                      <CheckCircleIcon
                                        sx={{
                                          color: theme.palette.success.main,
                                          fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                                          cursor: 'pointer'
                                        }}
                                      />
                                    </Tooltip>
                                    {onTakeBreak && (
                                      <Tooltip title={t('bookings.takeBreak', 'Take Break')} arrow>
                                        <IconButton
                                          size="small"
                                          onClick={() => handleTakeBreak(day.key, timeSlot, true)}
                                          sx={{
                                            color: theme.palette.warning.main,
                                            fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' },
                                            padding: '2px',
                                            '&:hover': {
                                              bgcolor: alpha(theme.palette.warning.main, 0.1)
                                            }
                                          }}
                                        >
                                          <RestIcon sx={{ fontSize: 'inherit' }} />
                                        </IconButton>
                                      </Tooltip>
                                    )}
                                  </Box>
                                ) : (
                                  <RadioButtonUncheckedIcon
                                    sx={{
                                      color: alpha(theme.palette.grey[400], 0.5),
                                      fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }
                                    }}
                                  />
                                )}
                              </Box>
                            );
                          })()}

                          {/* Second Half (30-60 minutes) */}
                          {(() => {
                            const secondHalfAvailable = isHalfHourSlotAvailable(day.key, timeSlot, false);
                            const secondHalfInPast = isHalfHourSlotInPast(day.key, timeSlot, false);

                            return (
                              <Box sx={{
                                flex: 1,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                bgcolor: secondHalfInPast
                                  ? alpha(theme.palette.grey[600], 0.15)
                                  : secondHalfAvailable
                                    ? alpha(theme.palette.success.main, 0.08)
                                    : alpha(theme.palette.grey[100], 0.3),
                                transition: 'all 0.2s ease'
                              }}>
                                {secondHalfInPast ? (
                                  <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.pastSlot', 'Past Time')}`} arrow>
                                    <RadioButtonUncheckedIcon
                                      sx={{
                                        color: theme.palette.grey[600],
                                        fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }
                                      }}
                                    />
                                  </Tooltip>
                                ) : secondHalfAvailable ? (
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                    <Tooltip title={`${timeSlot.hour.toString().padStart(2, '0')}:30${timeSlot.hour === 23 ? '-24:00' : `-${(timeSlot.hour + 1).toString().padStart(2, '0')}:00`} - ${t('bookings.availableSlot', 'Available')}`} arrow>
                                      <CheckCircleIcon
                                        sx={{
                                          color: theme.palette.success.main,
                                          fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                                          cursor: 'pointer'
                                        }}
                                      />
                                    </Tooltip>
                                    {onTakeBreak && (
                                      <Tooltip title={t('bookings.takeBreak', 'Take Break')} arrow>
                                        <IconButton
                                          size="small"
                                          onClick={() => handleTakeBreak(day.key, timeSlot, false)}
                                          sx={{
                                            color: theme.palette.warning.main,
                                            fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' },
                                            padding: '2px',
                                            '&:hover': {
                                              bgcolor: alpha(theme.palette.warning.main, 0.1)
                                            }
                                          }}
                                        >
                                          <RestIcon sx={{ fontSize: 'inherit' }} />
                                        </IconButton>
                                      </Tooltip>
                                    )}
                                  </Box>
                                ) : (
                                  <RadioButtonUncheckedIcon
                                    sx={{
                                      color: alpha(theme.palette.grey[400], 0.5),
                                      fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }
                                    }}
                                  />
                                )}
                              </Box>
                            );
                          })()}
                        </Box>
                      )}
                    </Box>
                  );
                })}
              </Box>
            );
          })}
        </Box>
      </Box>

      {/* Take Break Dialog */}
      <Dialog
        open={breakDialogOpen}
        onClose={() => setBreakDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
          <RestIcon sx={{ fontSize: '2rem', color: 'warning.main', mb: 1 }} />
          <Typography variant="h6" component="div">
            {t('bookings.takeBreakTitle', 'أخذ هذا الوقت راحة')}
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ textAlign: 'center', py: 3 }}>
          {selectedBreakSlot && (
            <Box>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {t('bookings.takeBreakMessage', 'هل تريد أخذ هذا الوقت راحة؟')}
              </Typography>

              <Box sx={{
                bgcolor: alpha(theme.palette.warning.main, 0.1),
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`
              }}>
                <Typography variant="h6" sx={{ color: 'warning.main', mb: 1 }}>
                  📅 {format(selectedBreakSlot.date, 'EEEE, MMM d, yyyy', { locale: isRtl ? ar : enUS })}
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                  🕐 {(() => {
                    const startHour = selectedBreakSlot.hour;
                    const startMinute = selectedBreakSlot.minute;
                    const endMinute = startMinute + 30;
                    const endHour = endMinute >= 60 ? startHour + 1 : startHour;
                    const finalEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;

                    return `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')} - ${endHour.toString().padStart(2, '0')}:${finalEndMinute.toString().padStart(2, '0')}`;
                  })()}
                </Typography>
              </Box>

              <Typography variant="body2" sx={{ mt: 2, color: 'text.secondary' }}>
                {t('bookings.takeBreakNote', 'سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط')}
              </Typography>
            </Box>
          )}
        </DialogContent>

        <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>
          <Button
            onClick={() => setBreakDialogOpen(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            {t('common.cancel', 'إلغاء')}
          </Button>
          <Button
            onClick={confirmTakeBreak}
            variant="contained"
            color="warning"
            sx={{ minWidth: 100, ml: 2 }}
          >
            {t('common.confirm', 'موافق')}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default WeeklyBookingsTable;
