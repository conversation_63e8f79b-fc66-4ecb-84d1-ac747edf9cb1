import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Paper,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Avatar,
  Chip,
  Grid,
  useTheme,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  VideoCall as VideoCallIcon,
  Cancel as CancelIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import axios from 'axios';
import { format, startOfWeek, addDays, addWeeks, subWeeks } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';
import WeeklyBookingsTable from '../../components/WeeklyBookingsTable';
import { convertFromDatabaseTime, formatDateInStudentTimezone, getCurrentTimeInTimezone, parseTimezoneOffset } from '../../utils/timezone';
import moment from 'moment-timezone';
import VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';

const TeacherBookings = () => {
  const { t, i18n } = useTranslation();
  const { token } = useAuth();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';

  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [teacherProfile, setTeacherProfile] = useState(null);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [cancellingBooking, setCancellingBooking] = useState(false);
  const [openMeeting, setOpenMeeting] = useState(false);
  const [currentMeeting, setCurrentMeeting] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [availableHours, setAvailableHours] = useState(null);
  const [weeklyBreaks, setWeeklyBreaks] = useState([]);

  // Week navigation
  const [currentWeekStart, setCurrentWeekStart] = useState(() => {
    const today = new Date();
    return startOfWeek(today, { weekStartsOn: 1 }); // Start from current week
  });

  // Days of the week (format expected by WeeklyBookingsTable)
  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  // Week navigation functions
  const goToPreviousWeek = () => {
    const previousWeek = subWeeks(currentWeekStart, 1);
    setCurrentWeekStart(previousWeek);
  };

  const goToNextWeek = () => {
    const nextWeek = addWeeks(currentWeekStart, 1);
    const today = new Date();
    const oneYearAhead = addWeeks(today, 52); // One year ahead from today
    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });

    // Don't allow going beyond one year ahead
    if (nextWeek <= maxWeek) {
      setCurrentWeekStart(nextWeek);
    }
  };

  // Check if navigation buttons should be disabled
  const isPreviousWeekDisabled = () => false;

  const isNextWeekDisabled = () => {
    const nextWeek = addWeeks(currentWeekStart, 1);
    const today = new Date();
    const oneYearAhead = addWeeks(today, 52); // One year ahead from today
    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });
    return nextWeek > maxWeek;
  };

  // Update current time every second
  useEffect(() => {
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timeInterval);
  }, []);

  // Fetch bookings
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setLoading(true);
        const { data } = await axios.get('/api/bookings/teacher', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (data.success) {
          console.log('Teacher bookings data:', data.data);

          // Make sure all bookings have the correct data types
          const processedBookings = data.data.map(booking => ({
            ...booking,
            price_per_lesson: parseFloat(booking.price_per_lesson || 0),
            duration: booking.duration ? String(booking.duration) : '50'
          }));

          console.log('Processed teacher bookings:', processedBookings);
          setBookings(processedBookings);

          // Set teacher profile with timezone information
          if (data.teacherTimezone) {
            setTeacherProfile({ timezone: data.teacherTimezone });
          }

          // Fetch teacher's available hours
          fetchAvailableHours();

          // Fetch weekly breaks
          fetchWeeklyBreaks();
        } else {
          setError(data.message || t('bookings.fetchError'));
        }
      } catch (error) {
        console.error('Error fetching teacher bookings:', error);
        setError(error.response?.data?.message || t('bookings.fetchError'));
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      fetchBookings();
    }
  }, [token, t]);

  // Fetch weekly breaks when week changes
  useEffect(() => {
    if (token) {
      fetchWeeklyBreaks();
    }
  }, [currentWeekStart, token]);

  // Fetch available hours
  const fetchAvailableHours = async () => {
    try {
      const { data } = await axios.get('/api/teacher/profile', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success && data.profile.available_hours) {
        const parsedHours = typeof data.profile.available_hours === 'string'
          ? JSON.parse(data.profile.available_hours)
          : data.profile.available_hours;
        console.log('Available hours loaded:', parsedHours);
        setAvailableHours(parsedHours);
      }
    } catch (error) {
      console.error('Error fetching available hours:', error);
    }
  };

  // Fetch weekly breaks
  const fetchWeeklyBreaks = async () => {
    try {
      // Use teacher's timezone instead of UTC
      const year = currentWeekStart.getFullYear();
      const month = String(currentWeekStart.getMonth() + 1).padStart(2, '0');
      const day = String(currentWeekStart.getDate()).padStart(2, '0');
      const weekStart = `${year}-${month}-${day}`;

      const { data } = await axios.get(`/api/teacher/weekly-breaks?week_start=${weekStart}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        console.log('🔍 FRONTEND: Weekly breaks received from server:', data.data);
        console.log('🔍 FRONTEND: Setting weeklyBreaks state to:', data.data);
        setWeeklyBreaks(data.data);
      }
    } catch (error) {
      console.error('Error fetching weekly breaks:', error);
    }
  };

  // Handle take break
  const handleTakeBreak = async (breakSlot) => {
    try {
      // Use teacher's timezone for both date and week_start
      const breakDate = breakSlot.date;
      const breakDateStr = `${breakDate.getFullYear()}-${String(breakDate.getMonth() + 1).padStart(2, '0')}-${String(breakDate.getDate()).padStart(2, '0')}`;

      const weekStartStr = `${currentWeekStart.getFullYear()}-${String(currentWeekStart.getMonth() + 1).padStart(2, '0')}-${String(currentWeekStart.getDate()).padStart(2, '0')}`;

      const breakData = {
        date: breakDateStr,
        hour: breakSlot.hour,
        minute: breakSlot.minute,
        week_start: weekStartStr
      };

      const { data } = await axios.post('/api/teacher/take-break', breakData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        // Add to weekly breaks list
        setWeeklyBreaks(prev => [...prev, `${breakData.date}_${breakData.hour}:${breakData.minute.toString().padStart(2, '0')}`]);

        // Show success message
        alert(t('bookings.breakTakenSuccess', 'تم أخذ الراحة بنجاح'));
      }
    } catch (error) {
      console.error('Error taking break:', error);
      alert(t('bookings.breakTakenError', 'خطأ في أخذ الراحة'));
    }
  };

  // Handle view details
  const handleViewDetails = (booking) => {
    setSelectedBooking(booking);
    setDetailsDialogOpen(true);
  };

  // Handle cancel booking
  const handleCancelBookingClick = (booking) => {
    setSelectedBooking(booking);
    setCancelDialogOpen(true);
  };

  // Handle join meeting
  const handleJoinMeeting = async (booking) => {
    try {
      // Check if room_name exists from the booking data
      if (!booking.room_name) {
        console.error('No room_name found for booking:', booking);
        alert(t('meetings.noRoomError') || 'Meeting room not found');
        return;
      }

      // Check if meeting_id exists
      if (!booking.meeting_id) {
        console.error('No meeting_id found for booking:', booking);
        alert(t('meetings.noMeetingError') || 'Meeting ID not found');
        return;
      }

      console.log('Joining meeting with data:', {
        room_name: booking.room_name,
        meeting_id: booking.meeting_id,
        datetime: booking.datetime,
        duration: booking.duration
      });

      // Validate room
      const response = await axios.get(`/meetings/${booking.room_name}/validate`);
      setCurrentMeeting({ ...booking, room_name: booking.room_name });
      setOpenMeeting(true);
    } catch (error) {
      console.error('Error joining meeting:', error);
      alert(t('meetings.joinError'));
    }
  };

  const handleCloseMeeting = () => {
    setOpenMeeting(false);
    setCurrentMeeting(null);
  };

  // Get meeting status from database directly
  const getMeetingStatus = (booking) => {
    return booking.status || 'scheduled';
  };

  // Check if user can join meeting
  const canJoinMeeting = (booking) => {
    if (!booking || !teacherProfile) return false;

    const currentStatus = getMeetingStatus(booking);
    if (currentStatus === 'cancelled' || currentStatus === 'completed') {
      return false;
    }

    const meetingStartTime = new Date(booking.datetime);
    const meetingEndTime = new Date(booking.datetime);
    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));
    const now = new Date();

    return now >= meetingStartTime && now < meetingEndTime;
  };

  // Handle booking cancellation
  const handleCancelBooking = async () => {
    if (!selectedBooking) return;

    try {
      setCancellingBooking(true);
      const { data } = await axios.put(`/bookings/${selectedBooking.id}/cancel`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        // Update the booking status in the local state
        setBookings(prevBookings =>
          prevBookings.map(booking =>
            booking.id === selectedBooking.id
              ? { ...booking, status: 'cancelled' }
              : booking
          )
        );
        alert(t('bookings.cancelSuccess'));
      } else {
        alert(data.message || t('bookings.cancelError'));
      }
    } catch (error) {
      console.error('Error cancelling booking:', error);
      alert(error.response?.data?.message || t('bookings.cancelError'));
    } finally {
      setCancellingBooking(false);
      setCancelDialogOpen(false);
      setDetailsDialogOpen(false);
      setSelectedBooking(null);
    }
  };

  // Get status chip color
  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled':
        return 'primary';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      case 'issue_reported':
        return 'warning';
      case 'ongoing':
        return 'info';
      default:
        return 'default';
    }
  };

  // Get translated status text
  const getStatusText = (status) => {
    return t(`bookings.statusValues.${status}`, { 
      defaultValue: status.charAt(0).toUpperCase() + status.slice(1) 
    });
  };

  // Format booking date in teacher's timezone
  const formatBookingDate = (datetime) => {
    if (!teacherProfile || !teacherProfile.timezone) {
      return format(new Date(datetime), 'PPP', { locale: isRtl ? ar : enUS });
    }

    const formattedDate = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD');
    return moment(formattedDate, 'YYYY-MM-DD').format('MMMM D, YYYY');
  };

  // Format booking time in teacher's timezone
  const formatBookingTime = (datetime) => {
    if (!teacherProfile || !teacherProfile.timezone) {
      return format(new Date(datetime), 'p', { locale: isRtl ? ar : enUS });
    }

    const formattedDateTime = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
    return moment(formattedDateTime, 'YYYY-MM-DD HH:mm:ss').format('h:mm A');
  };

  // Calculate lesson price based on duration
  const calculateLessonPrice = (pricePerLesson, duration) => {
    const durationNum = parseInt(duration, 10);
    return durationNum === 50 ? pricePerLesson : pricePerLesson / 2;
  };

  // Render details dialog
  const renderDetailsDialog = () => {
    if (!selectedBooking) return null;

    const lessonPrice = calculateLessonPrice(selectedBooking.price_per_lesson, selectedBooking.duration);

    return (
      <Dialog
        open={detailsDialogOpen}
        onClose={() => setDetailsDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CalendarIcon />
            <Typography variant="h6">
              {t('bookings.bookingDetails')}
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  src={selectedBooking.student_picture}
                  alt={selectedBooking.student_name}
                  sx={{ mr: 2, width: 56, height: 56 }}
                />
                <Box>
                  <Typography variant="h6">
                    {selectedBooking.student_name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedBooking.student_email}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CalendarIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle2" color="text.secondary">
                  {t('bookings.date')}
                </Typography>
              </Box>
              <Typography variant="body1">
                {formatBookingDate(selectedBooking.datetime)}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle2" color="text.secondary">
                  {t('bookings.time')}
                </Typography>
              </Box>
              <Typography variant="body1">
                {formatBookingTime(selectedBooking.datetime)}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle2" color="text.secondary">
                  {t('bookings.duration')}
                </Typography>
              </Box>
              <Typography variant="body1">
                {selectedBooking.duration} {t('bookings.minutes')}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <MoneyIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle2" color="text.secondary">
                  {t('bookings.price')}
                </Typography>
              </Box>
              <Typography variant="body1">
                ${lessonPrice.toFixed(2)}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle2" color="text.secondary">
                  {t('bookings.status')}
                </Typography>
              </Box>
              <Chip
                label={getStatusText(selectedBooking.status)}
                color={getStatusColor(selectedBooking.status)}
                variant="filled"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>
            {t('common.close')}
          </Button>

          {/* Join Meeting Button */}
          {selectedBooking && (
            <Button
              onClick={() => canJoinMeeting(selectedBooking) && handleJoinMeeting(selectedBooking)}
              color={canJoinMeeting(selectedBooking) ? "success" : "inherit"}
              variant={canJoinMeeting(selectedBooking) ? "contained" : "outlined"}
              startIcon={<VideoCallIcon />}
              disabled={!canJoinMeeting(selectedBooking)}
              sx={{
                mr: 1,
                ...(canJoinMeeting(selectedBooking) ? {} : {
                  color: theme.palette.grey[500],
                  borderColor: theme.palette.grey[300],
                  backgroundColor: theme.palette.grey[100],
                  '&:hover': {
                    backgroundColor: theme.palette.grey[200],
                  }
                })
              }}
            >
              {canJoinMeeting(selectedBooking) ? t('meetings.join') : t('meetings.notStarted')}
            </Button>
          )}

          {selectedBooking?.status === 'scheduled' && (
            <Button
              onClick={() => {
                setDetailsDialogOpen(false);
                setCancelDialogOpen(true);
              }}
              color="error"
              variant="contained"
              startIcon={<CancelIcon />}
            >
              {t('bookings.cancel')}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    );
  };

  // Cancel confirmation dialog
  const renderCancelDialog = () => (
    <Dialog open={cancelDialogOpen} onClose={() => setCancelDialogOpen(false)}>
      <DialogTitle>
        {t('bookings.confirmCancel')}
        <IconButton
          aria-label="close"
          onClick={() => setCancelDialogOpen(false)}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body1">
          {t('bookings.cancelWarning')}
        </Typography>
        {selectedBooking && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" gutterBottom>
              <strong>{t('bookings.student')}:</strong> {selectedBooking.student_name}
            </Typography>
            <Typography variant="body2" gutterBottom>
              <strong>{t('bookings.date')}:</strong> {formatBookingDate(selectedBooking.datetime)}
            </Typography>
            <Typography variant="body2">
              <strong>{t('bookings.time')}:</strong> {formatBookingTime(selectedBooking.datetime)}
            </Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setCancelDialogOpen(false)}>
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleCancelBooking}
          color="error"
          variant="contained"
          disabled={cancellingBooking}
        >
          {cancellingBooking ? t('bookings.cancelling') : t('bookings.confirmCancelButton')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper elevation={3} sx={{ p: 3, mb: 4, bgcolor: 'primary.main', color: 'white' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
            <Box>
              <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                {t('teacher.weeklyBookings')}
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                {t('teacher.weeklyBookingsDescription')}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="body2" sx={{ opacity: 0.8, mb: 0.5 }}>
                  {t('booking.weekNavigation')}
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  📅 {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title={t('booking.previousWeek')}>
                  <span>
                    <IconButton
                      onClick={goToPreviousWeek}
                      disabled={isPreviousWeekDisabled()}
                      sx={{
                        color: 'white',
                        bgcolor: 'rgba(255, 255, 255, 0.1)',
                        '&:hover': {
                          bgcolor: 'rgba(255, 255, 255, 0.2)',
                        },
                        '&:disabled': {
                          color: 'rgba(255, 255, 255, 0.3)',
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                        }
                      }}
                    >
                      <ChevronLeftIcon />
                    </IconButton>
                  </span>
                </Tooltip>
                <Tooltip title={t('booking.nextWeek')}>
                  <span>
                    <IconButton
                      onClick={goToNextWeek}
                      disabled={isNextWeekDisabled()}
                      sx={{
                        color: 'white',
                        bgcolor: 'rgba(255, 255, 255, 0.1)',
                        '&:hover': {
                          bgcolor: 'rgba(255, 255, 255, 0.2)',
                        },
                        '&:disabled': {
                          color: 'rgba(255, 255, 255, 0.3)',
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                        }
                      }}
                    >
                      <ChevronRightIcon />
                    </IconButton>
                  </span>
                </Tooltip>
              </Box>
            </Box>
          </Box>
        </Paper>

        {/* Current Time Display */}
        <Paper elevation={2} sx={{ p: 2, mb: 3, bgcolor: 'background.paper' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box sx={{
              bgcolor: 'primary.main',
              color: 'white',
              p: 1,
              borderRadius: 1,
              minWidth: 40,
              textAlign: 'center'
            }}>
              🕐
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                {t('bookings.currentTime')}
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {teacherProfile?.timezone ? (
                  moment().utc().add(parseTimezoneOffset(teacherProfile.timezone), 'minutes').format('dddd, MMMM D, YYYY [at] h:mm A')
                ) : (
                  format(new Date(), 'PPpp', {
                    locale: isRtl ? ar : enUS
                  })
                )}
              </Typography>
              {teacherProfile?.timezone && (
                <Typography variant="caption" sx={{ opacity: 0.8 }}>
                  {teacherProfile.timezone}
                </Typography>
              )}
            </Box>
          </Box>
        </Paper>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        ) : (
          <WeeklyBookingsTable
            bookings={bookings}
            loading={loading}
            currentWeekStart={currentWeekStart}
            daysOfWeek={daysOfWeek}
            onViewDetails={handleViewDetails}
            onCancelBooking={handleCancelBookingClick} // Now teachers can cancel bookings
            studentProfile={teacherProfile} // Pass teacher profile for timezone conversion
            formatBookingTime={formatBookingTime}
            getStatusColor={getStatusColor}
            getStatusText={getStatusText}
            isTeacherView={true} // Add this prop to distinguish teacher view
            availableHours={availableHours} // Pass available hours to show available slots
            onTakeBreak={handleTakeBreak} // Pass take break handler
            weeklyBreaks={weeklyBreaks} // Pass weekly breaks
          />
        )}

        {renderDetailsDialog()}
        {renderCancelDialog()}
      </Container>

      {/* Meeting Dialog */}
      <Dialog
        fullScreen
        open={openMeeting}
        onClose={handleCloseMeeting}
      >
        {currentMeeting && (
          <VideoSDKMeeting
            roomId={currentMeeting.room_name}
            meetingId={currentMeeting.meeting_id}
            meetingData={currentMeeting}
            onClose={handleCloseMeeting}
          />
        )}
      </Dialog>
    </Layout>
  );
};

export default TeacherBookings;
