<?php
/**
 * Timezone Debug Page
 * This page helps diagnose timezone conversion issues in the teacher weekly breaks system
 */

require_once 'server/config/db.config.php';

try {
    // Create database connection
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['database']}", 
                   $config['user'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html>";
    echo "<html lang='en'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    echo "<title>Timezone Debug - Teacher Weekly Breaks</title>";
    echo "<style>";
    echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
    echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
    echo ".section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
    echo ".section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }";
    echo ".section h3 { color: #666; margin-top: 20px; }";
    echo ".data-row { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; }";
    echo ".error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px; }";
    echo ".success { background: #d4edda; color: #155724; padding: 10px; border-radius: 3px; }";
    echo ".warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 3px; }";
    echo ".info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 3px; }";
    echo ".step { background: #e9ecef; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }";
    echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
    echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }";
    echo "th { background: #f8f9fa; font-weight: bold; }";
    echo ".highlight { background: #fff3cd; font-weight: bold; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    
    echo "<div class='container'>";
    echo "<h1>🔍 Timezone Debug - Teacher Weekly Breaks</h1>";
    echo "<p>This page helps diagnose timezone conversion issues in the teacher weekly breaks system.</p>";
    
    // Get teacher profile ID 32 (from the screenshot)
    $teacherProfileId = 32;
    
    // Section 1: Raw Database Data
    echo "<div class='section'>";
    echo "<h2>📊 1. Raw Database Data</h2>";
    
    // Get teacher info
    $stmt = $pdo->prepare("
        SELECT tp.id, tp.timezone, u.full_name 
        FROM teacher_profiles tp 
        JOIN users u ON tp.user_id = u.id 
        WHERE tp.id = ?
    ");
    $stmt->execute([$teacherProfileId]);
    $teacher = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($teacher) {
        echo "<div class='info'>";
        echo "<strong>Teacher:</strong> {$teacher['full_name']}<br>";
        echo "<strong>Profile ID:</strong> {$teacher['id']}<br>";
        echo "<strong>Timezone:</strong> {$teacher['timezone']}";
        echo "</div>";
    } else {
        echo "<div class='error'>Teacher with profile ID {$teacherProfileId} not found!</div>";
        echo "</div></div></body></html>";
        exit;
    }
    
    // Get all breaks for this teacher
    $stmt = $pdo->prepare("
        SELECT id, datetime, created_at 
        FROM teacher_weekly_breaks 
        WHERE teacher_profile_id = ? 
        ORDER BY datetime
    ");
    $stmt->execute([$teacherProfileId]);
    $breaks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📅 Weekly Breaks in Database:</h3>";
    if (empty($breaks)) {
        echo "<div class='warning'>No breaks found for this teacher.</div>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>DateTime (UTC)</th><th>Created At</th></tr>";
        foreach ($breaks as $break) {
            echo "<tr>";
            echo "<td>{$break['id']}</td>";
            echo "<td class='highlight'>{$break['datetime']}</td>";
            echo "<td>{$break['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    // Section 2: Timezone Parsing
    echo "<div class='section'>";
    echo "<h2>⚙️ 2. Timezone Parsing</h2>";
    
    $teacherTimezone = $teacher['timezone'];
    echo "<div class='step'>";
    echo "<strong>Teacher Timezone:</strong> {$teacherTimezone}<br>";
    
    // Parse timezone offset
    if (preg_match('/UTC([+-])(\d{2}):(\d{2})/', $teacherTimezone, $matches)) {
        $sign = $matches[1] === '+' ? 1 : -1;
        $hours = intval($matches[2]);
        $minutes = intval($matches[3]);
        $offsetMinutes = $sign * ($hours * 60 + $minutes);
        
        echo "<strong>Parsed Successfully:</strong><br>";
        echo "- Sign: {$matches[1]}<br>";
        echo "- Hours: {$hours}<br>";
        echo "- Minutes: {$minutes}<br>";
        echo "- Total Offset Minutes: {$offsetMinutes}<br>";
        
        if ($offsetMinutes > 0) {
            echo "- This means teacher is {$offsetMinutes} minutes AHEAD of UTC";
        } else {
            echo "- This means teacher is " . abs($offsetMinutes) . " minutes BEHIND UTC";
        }
    } else {
        echo "<div class='error'>Failed to parse timezone: {$teacherTimezone}</div>";
        $offsetMinutes = 0;
    }
    echo "</div>";
    echo "</div>";
    
    // Section 3: Conversion Analysis
    if (!empty($breaks)) {
        echo "<div class='section'>";
        echo "<h2>🔄 3. Timezone Conversion Analysis</h2>";
        
        foreach ($breaks as $index => $break) {
            echo "<div class='step'>";
            echo "<h3>Break #" . ($index + 1) . " (ID: {$break['id']})</h3>";
            
            $utcDatetime = $break['datetime'];
            echo "<strong>📊 Raw from DB:</strong> {$utcDatetime}<br>";
            
            // Parse UTC datetime
            $utcTimestamp = strtotime($utcDatetime);
            if ($utcTimestamp === false) {
                echo "<div class='error'>Invalid datetime format!</div>";
                continue;
            }
            
            echo "<strong>🌍 UTC Timestamp:</strong> {$utcTimestamp}<br>";
            echo "<strong>🌍 UTC ISO:</strong> " . gmdate('Y-m-d H:i:s', $utcTimestamp) . "<br>";
            
            // Convert to teacher timezone
            $teacherTimestamp = $utcTimestamp + ($offsetMinutes * 60);
            $teacherDatetime = gmdate('Y-m-d H:i:s', $teacherTimestamp);
            
            echo "<strong>🏠 Teacher Timestamp:</strong> {$teacherTimestamp}<br>";
            echo "<strong>🏠 Teacher DateTime:</strong> {$teacherDatetime}<br>";
            
            // Break down teacher datetime
            $teacherDate = gmdate('Y-m-d', $teacherTimestamp);
            $teacherTime = gmdate('H:i', $teacherTimestamp);
            $teacherHour = gmdate('H', $teacherTimestamp);
            $teacherMinute = gmdate('i', $teacherTimestamp);
            $teacherDayName = gmdate('l', $teacherTimestamp);
            
            echo "<strong>📅 Teacher Date:</strong> {$teacherDate}<br>";
            echo "<strong>⏰ Teacher Time:</strong> {$teacherTime}<br>";
            echo "<strong>📍 Teacher Day:</strong> {$teacherDayName}<br>";
            
            // Show what the frontend should see
            echo "<div class='info'>";
            echo "<strong>🎯 Expected Frontend Result:</strong><br>";
            echo "- Should hide slot on: <strong>{$teacherDate}</strong><br>";
            echo "- At time: <strong>{$teacherTime}</strong><br>";
            echo "- Day: <strong>{$teacherDayName}</strong>";
            echo "</div>";
            
            // Show different conversion methods
            echo "<h4>🔬 Different Conversion Methods:</h4>";
            
            // Method 1: JavaScript-style (what backend currently does)
            $jsDate = new DateTime($utcDatetime, new DateTimeZone('UTC'));
            $jsDate->modify("+{$offsetMinutes} minutes");
            echo "<strong>Method 1 (JavaScript-style):</strong> " . $jsDate->format('Y-m-d H:i:s') . "<br>";
            
            // Method 2: PHP DateTime with timezone
            try {
                $phpDate = new DateTime($utcDatetime, new DateTimeZone('UTC'));
                $targetTz = $offsetMinutes >= 0 ? "+{$offsetMinutes}" : "{$offsetMinutes}";
                $phpDate->setTimezone(new DateTimeZone($targetTz));
                echo "<strong>Method 2 (PHP DateTime):</strong> " . $phpDate->format('Y-m-d H:i:s') . "<br>";
            } catch (Exception $e) {
                echo "<strong>Method 2 (PHP DateTime):</strong> Error - " . $e->getMessage() . "<br>";
            }
            
            // Method 3: Manual calculation (what we implemented)
            $manualTimestamp = strtotime($utcDatetime) + ($offsetMinutes * 60);
            echo "<strong>Method 3 (Manual calc):</strong> " . gmdate('Y-m-d H:i:s', $manualTimestamp) . "<br>";
            
            echo "</div>";
        }
        echo "</div>";
    }
    
    // Section 4: Current Time Analysis
    echo "<div class='section'>";
    echo "<h2>🕐 4. Current Time Analysis</h2>";
    
    $currentUtc = gmdate('Y-m-d H:i:s');
    $currentLocal = date('Y-m-d H:i:s');
    $currentTeacher = gmdate('Y-m-d H:i:s', time() + ($offsetMinutes * 60));
    
    echo "<div class='step'>";
    echo "<strong>🌍 Current UTC:</strong> {$currentUtc}<br>";
    echo "<strong>🖥️ Server Local:</strong> {$currentLocal}<br>";
    echo "<strong>🏠 Teacher Time:</strong> {$currentTeacher}<br>";
    echo "</div>";
    echo "</div>";
    
    // Section 5: Frontend Simulation
    echo "<div class='section'>";
    echo "<h2>💻 5. Frontend Simulation</h2>";
    echo "<p>This simulates what the frontend should receive and how it should process the data.</p>";
    
    if (!empty($breaks)) {
        echo "<h3>📤 Backend Response (what API should send):</h3>";
        echo "<div class='data-row'>";
        echo "{\n";
        echo "  \"success\": true,\n";
        echo "  \"data\": [\n";
        foreach ($breaks as $index => $break) {
            echo "    {\n";
            echo "      \"datetime\": \"{$break['datetime']}\"\n";
            echo "    }";
            if ($index < count($breaks) - 1) echo ",";
            echo "\n";
        }
        echo "  ]\n";
        echo "}";
        echo "</div>";
        
        echo "<h3>🔄 Frontend Processing (what should happen):</h3>";
        foreach ($breaks as $index => $break) {
            echo "<div class='step'>";
            echo "<strong>Break #" . ($index + 1) . ":</strong><br>";
            echo "1. Receive UTC: {$break['datetime']}<br>";
            echo "2. Convert to teacher timezone using formatDateInStudentTimezone()<br>";
            
            $teacherTimestamp = strtotime($break['datetime']) + ($offsetMinutes * 60);
            $teacherDatetime = gmdate('Y-m-d H:i:s', $teacherTimestamp);
            $teacherDate = gmdate('Y-m-d', $teacherTimestamp);
            $teacherTime = gmdate('H:i', $teacherTimestamp);
            
            echo "3. Result: {$teacherDatetime}<br>";
            echo "4. Check if slot matches: Date={$teacherDate}, Time={$teacherTime}<br>";
            echo "5. Hide slot if match found<br>";
            echo "</div>";
        }
    }
    echo "</div>";
    
    // Section 6: Recommendations
    echo "<div class='section'>";
    echo "<h2>💡 6. Recommendations</h2>";
    
    echo "<div class='success'>";
    echo "<strong>✅ What should work correctly:</strong><br>";
    echo "1. Backend stores breaks in UTC<br>";
    echo "2. Backend sends UTC datetime objects to frontend<br>";
    echo "3. Frontend converts UTC to teacher timezone<br>";
    echo "4. Frontend compares converted time with slot time<br>";
    echo "5. Frontend hides matching slots";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<strong>⚠️ Common Issues:</strong><br>";
    echo "1. Timezone offset calculation errors<br>";
    echo "2. Date object timezone interference<br>";
    echo "3. String vs object format mismatches<br>";
    echo "4. Day boundary crossing issues<br>";
    echo "5. Browser timezone affecting calculations";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<strong>🔧 Debug Steps:</strong><br>";
    echo "1. Check browser console for frontend logs<br>";
    echo "2. Verify API response format<br>";
    echo "3. Test timezone conversion functions<br>";
    echo "4. Compare expected vs actual slot hiding<br>";
    echo "5. Check for JavaScript Date object issues";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</body>";
    echo "</html>";
    
} catch (Exception $e) {
    echo "<!DOCTYPE html><html><body>";
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border-radius: 5px;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    echo "</body></html>";
}
?>
