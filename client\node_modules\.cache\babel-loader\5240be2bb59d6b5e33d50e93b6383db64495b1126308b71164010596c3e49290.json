{"ast": null, "code": "var _jsxFileName = \"D:\\\\xampp\\\\htdocs\\\\allemnionline\\\\client\\\\src\\\\pages\\\\student\\\\BookingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Paper, Typography, Box, Grid, Button, CircularProgress, Alert, Chip, Card, CardContent, CardActions, Divider, Avatar, Dialog, DialogTitle, DialogContent, DialogActions, useTheme, useMediaQuery, IconButton, Tooltip, RadioGroup, FormControlLabel, Radio, FormControl, FormLabel } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, CalendarMonth as CalendarMonthIcon, AccessTime as AccessTimeIcon, CheckCircle as CheckCircleIcon, Info as InfoIcon, Close as CloseIcon, ChevronLeft as ChevronLeftIcon, ChevronRight as ChevronRightIcon } from '@mui/icons-material';\nimport axios from '../../utils/axios';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Layout from '../../components/Layout';\nimport BookableHoursTable from '../../components/BookableHoursTable';\nimport ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';\nimport { format, addDays, startOfWeek, isSameDay, addWeeks, subWeeks } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport { convertBookingDateTime, getCurrentTimeInTimezone, formatDateInStudentTimezone, convertFromDatabaseTime, parseTimezoneOffset } from '../../utils/timezone';\nimport moment from 'moment-timezone';\nimport { toast } from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BookingPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const {\n    currentUser,\n    token\n  } = useAuth();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const isRtl = i18n.language === 'ar';\n  const [teacher, setTeacher] = useState(null);\n  const [isTrialEligible, setIsTrialEligible] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedSlot, setSelectedSlot] = useState(null);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const [bookingError, setBookingError] = useState(null);\n  const [bookingData, setBookingData] = useState(null);\n  const [currentWeekStart, setCurrentWeekStart] = useState(() => {\n    const today = new Date();\n    return startOfWeek(today, {\n      weekStartsOn: 1\n    }); // Start from current week\n  });\n  const [lessonDuration, setLessonDuration] = useState('25'); // Default to half lesson (25 minutes)\n  const [studentProfile, setStudentProfile] = useState(null);\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  // Days of the week\n  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n\n  // Week navigation functions\n  const goToPreviousWeek = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const lastWeek = startOfWeek(subWeeks(today, 1), {\n      weekStartsOn: 1\n    });\n    if (previousWeek >= lastWeek) {\n      setCurrentWeekStart(previousWeek);\n    }\n  };\n  const goToNextWeek = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, {\n      weekStartsOn: 1\n    });\n\n    // Don't allow going beyond one year ahead\n    if (nextWeek <= maxWeek) {\n      setCurrentWeekStart(nextWeek);\n    }\n  };\n\n  // Check if navigation buttons should be disabled\n  const isPreviousWeekDisabled = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const lastWeek = startOfWeek(subWeeks(today, 1), {\n      weekStartsOn: 1\n    });\n    return previousWeek < lastWeek;\n  };\n  const isNextWeekDisabled = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, {\n      weekStartsOn: 1\n    });\n    return nextWeek > maxWeek;\n  };\n\n  // Fetch student profile\n  useEffect(() => {\n    const fetchStudentProfile = async () => {\n      if (!token) return;\n      try {\n        const {\n          data\n        } = await axios.get('/api/students/profile', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (data.success && data.profile) {\n          setStudentProfile(data.profile);\n        }\n      } catch (error) {\n        console.error('Error fetching student profile:', error);\n      }\n    };\n    fetchStudentProfile();\n  }, [token]);\n\n  // Fetch teacher details\n  useEffect(() => {\n    const fetchTeacherDetails = async () => {\n      if (!id || !token) return;\n      try {\n        setLoading(true);\n        const {\n          data\n        } = await axios.get(`/teachers/${id}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (data.success) {\n          setTeacher(data.data);\n          // After teacher details load, check if this is the first booking between student & teacher\n          try {\n            const hasBookedRes = await axios.get(`/api/bookings/student/hasBooked/${id}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              }\n            });\n            if (hasBookedRes.data.success) {\n              setIsTrialEligible(!hasBookedRes.data.hasPrevious);\n            }\n          } catch (e) {\n            console.error('Error checking previous bookings', e);\n          }\n        } else {\n          setError(data.message || t('teacherDetails.errorFetching'));\n        }\n      } catch (error) {\n        console.error('Error fetching teacher details:', error);\n        setError(t('teacherDetails.errorFetching'));\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchTeacherDetails();\n  }, [id, token, t]);\n\n  // Update current time every second\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Get current time in student's timezone (same method as meetings page)\n  const getCurrentTimeInStudentTimezone = () => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return new Date();\n    }\n    // Use the same method as the time display at the top\n    const currentTimeFormatted = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    return moment(currentTimeFormatted, 'YYYY-MM-DD HH:mm:ss').toDate();\n  };\n\n  // Check if a time slot is in the past\n  const isSlotInPast = (day, timeSlotKey) => {\n    const date = addDays(currentWeekStart, daysOfWeek.indexOf(day));\n    const [startTime] = timeSlotKey.split('-');\n    const [hours, minutes] = startTime.split(':').map(Number);\n\n    // Create slot datetime in student's timezone\n    const dateStr = format(date, 'yyyy-MM-dd');\n    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n\n    // Create the slot datetime and current time using the same method as the time display\n    let slotDateTime;\n    let now;\n    if (studentProfile && studentProfile.timezone) {\n      // Create slot datetime in student's timezone using moment-timezone\n      const slotDateTimeStr = `${dateStr} ${timeStr}:00`;\n\n      // Parse the slot time as if it's in the student's timezone\n      const offsetMinutes = parseTimezoneOffset(studentProfile.timezone);\n      const offsetHours = offsetMinutes / 60;\n      const sign = offsetHours >= 0 ? '+' : '-';\n      const absHours = Math.abs(offsetHours);\n      const hours_offset = Math.floor(absHours);\n      const minutes_offset = Math.round((absHours - hours_offset) * 60);\n      const momentTimezone = `${sign}${String(hours_offset).padStart(2, '0')}:${String(minutes_offset).padStart(2, '0')}`;\n\n      // Create slot datetime in student's timezone\n      const slotMoment = moment(slotDateTimeStr, 'YYYY-MM-DD HH:mm:ss').utcOffset(momentTimezone);\n      slotDateTime = slotMoment.toDate();\n\n      // Get current time in student's timezone using the same method as display\n      const currentTimeFormatted = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n      now = moment(currentTimeFormatted, 'YYYY-MM-DD HH:mm:ss').toDate();\n    } else {\n      // Fallback to local time\n      slotDateTime = new Date(date);\n      slotDateTime.setHours(hours, minutes, 0, 0);\n      now = new Date();\n    }\n    const isPast = slotDateTime < now;\n    return isPast;\n  };\n\n  // Handle slot selection\n  const handleSelectSlot = (day, timeSlot) => {\n    const date = addDays(currentWeekStart, daysOfWeek.indexOf(day));\n    setSelectedSlot({\n      day,\n      timeSlot,\n      date,\n      formattedDate: format(date, 'yyyy-MM-dd'),\n      formattedTime: timeSlot\n    });\n    setConfirmDialogOpen(true);\n  };\n\n  // Get consecutive hour blocks for a day\n  const getConsecutiveHourBlocks = daySlots => {\n    if (!daySlots || daySlots.length === 0) return [];\n    const blocks = [];\n    const sortedSlots = [...daySlots].sort();\n    for (let i = 0; i < sortedSlots.length; i++) {\n      const currentSlot = sortedSlots[i];\n      const [startTime] = currentSlot.split('-');\n      const [hours, minutes] = startTime.split(':').map(Number);\n\n      // Only process slots that start at :00 (beginning of hour)\n      if (minutes === 0) {\n        // Check if the next 30-minute slot is also available\n        let nextSlot;\n        if (hours === 23) {\n          // Special case for 23:00 - next slot is 23:30-00:00\n          nextSlot = `23:30-00:00`;\n        } else {\n          nextSlot = `${hours.toString().padStart(2, '0')}:30-${(hours + 1).toString().padStart(2, '0')}:00`;\n        }\n        if (sortedSlots.includes(nextSlot)) {\n          // This is a full hour block\n          blocks.push({\n            type: 'full',\n            startSlot: currentSlot,\n            endSlot: nextSlot,\n            hour: hours\n          });\n          // Skip the next slot since it's part of this full hour\n          i++;\n        } else {\n          // This is just a half hour\n          blocks.push({\n            type: 'half',\n            slot: currentSlot,\n            hour: hours,\n            minute: minutes\n          });\n        }\n      } else {\n        // This is a :30 slot, check if it's not part of a full hour we already processed\n        const prevSlot = `${hours.toString().padStart(2, '0')}:00-${hours.toString().padStart(2, '0')}:30`;\n        if (!sortedSlots.includes(prevSlot)) {\n          // This is just a half hour\n          blocks.push({\n            type: 'half',\n            slot: currentSlot,\n            hour: hours,\n            minute: minutes\n          });\n        }\n      }\n    }\n    return blocks;\n  };\n\n  // Check if a full hour is available for the selected slot\n  const isFullHourAvailable = (day, timeSlotKey, availableHours) => {\n    if (!availableHours || !availableHours[day]) return false;\n    const blocks = getConsecutiveHourBlocks(availableHours[day]);\n    const fullHourBlock = blocks.find(block => block.type === 'full' && (block.startSlot === timeSlotKey || block.endSlot === timeSlotKey));\n    if (!fullHourBlock) return false;\n\n    // Check if both slots are available (not in the past)\n    const firstSlotPast = isSlotInPast(day, fullHourBlock.startSlot);\n    const secondSlotPast = isSlotInPast(day, fullHourBlock.endSlot);\n\n    // Full hour is only available if both slots are not in the past\n    return !firstSlotPast && !secondSlotPast;\n  };\n\n  // Check if cross-hour booking is available\n  const checkCrossHourAvailability = (day, timeSlotKey, availableHours) => {\n    if (!availableHours || !availableHours[day]) return false;\n    const [startTime] = timeSlotKey.split('-');\n    const [hours, minutes] = startTime.split(':').map(Number);\n\n    // Cross-hour booking is only possible if this is a :30 slot\n    if (minutes !== 30) return false;\n\n    // Check if the next hour's first half is available\n    const nextHourFirstHalf = `${((hours + 1) % 24).toString().padStart(2, '0')}:00-${((hours + 1) % 24).toString().padStart(2, '0')}:30`;\n\n    // Check if next hour slot exists in available hours\n    if (!availableHours[day].includes(nextHourFirstHalf)) return false;\n\n    // Check if both slots are not in the past\n    const currentSlotPast = isSlotInPast(day, timeSlotKey);\n    const nextSlotPast = isSlotInPast(day, nextHourFirstHalf);\n    return !currentSlotPast && !nextSlotPast;\n  };\n\n  // Check if second half of hour is available for booking with flexible options\n  const checkSecondHalfAvailability = (day, timeSlotKey, availableHours) => {\n    if (!availableHours || !availableHours[day]) return false;\n    const [startTime] = timeSlotKey.split('-');\n    const [hours, minutes] = startTime.split(':').map(Number);\n\n    // For :00 slots, check if the second half (:30) is available\n    if (minutes === 0) {\n      const secondHalfSlot = `${hours.toString().padStart(2, '0')}:30-${((hours + 1) % 24).toString().padStart(2, '0')}:00`;\n\n      // Check if second half slot exists and is not in the past\n      if (availableHours[day].includes(secondHalfSlot)) {\n        const secondHalfPast = isSlotInPast(day, secondHalfSlot);\n        if (!secondHalfPast) {\n          // Check if the slot after the second half is also available (for full lesson option)\n          const nextHourFirstHalf = `${((hours + 1) % 24).toString().padStart(2, '0')}:00-${((hours + 1) % 24).toString().padStart(2, '0')}:30`;\n          const hasNextSlot = availableHours[day].includes(nextHourFirstHalf) && !isSlotInPast(day, nextHourFirstHalf);\n          return {\n            canBookSecondHalf: true,\n            canBookFullFromSecondHalf: hasNextSlot\n          };\n        }\n      }\n    }\n\n    // For :30 slots, check if the next hour's first half is available (for full lesson option)\n    if (minutes === 30) {\n      const nextHourFirstHalf = `${((hours + 1) % 24).toString().padStart(2, '0')}:00-${((hours + 1) % 24).toString().padStart(2, '0')}:30`;\n      const hasNextSlot = availableHours[day].includes(nextHourFirstHalf) && !isSlotInPast(day, nextHourFirstHalf);\n      return {\n        canBookSecondHalf: true,\n        canBookFullFromSecondHalf: hasNextSlot\n      };\n    }\n    return {\n      canBookSecondHalf: false,\n      canBookFullFromSecondHalf: false\n    };\n  };\n\n  // Check if full hour lesson is possible for selected slot (supports cross-day)\n  const canBookFullHour = (day, timeSlotKey) => {\n    if (!(teacher !== null && teacher !== void 0 && teacher.available_hours)) return false;\n    try {\n      const availableHours = typeof teacher.available_hours === 'string' ? JSON.parse(teacher.available_hours) : teacher.available_hours;\n\n      // Extract hour/minute of the chosen slot\n      const [startTime] = timeSlotKey.split('-');\n      const [hours, minutes] = startTime.split(':').map(Number);\n\n      // Helper – make sure the given slot is available on the given day and not in the past\n      const isSlotAvailableAndFuture = (checkDay, slotKey) => {\n        const slots = availableHours[checkDay] || [];\n        return slots.includes(slotKey) && !isSlotInPast(checkDay, slotKey);\n      };\n\n      // If user picked the first half of an hour (:00-:30), we only need the second half on the SAME day\n      if (minutes === 0) {\n        const secondHalfSlot = `${hours.toString().padStart(2, '0')}:30-${((hours + 1) % 24).toString().padStart(2, '0')}:00`;\n        return isSlotAvailableAndFuture(day, secondHalfSlot);\n      }\n\n      // If user picked the second half of an hour (:30-:00), we need the next hour's first half\n      if (minutes === 30) {\n        // Calculate next hour and whether this crosses to the next calendar day\n        const nextHour = (hours + 1) % 24;\n        const nextHourStr = nextHour.toString().padStart(2, '0');\n        const nextHourFirstHalf = `${nextHourStr}:00-${nextHourStr}:30`;\n        const currentDayIndex = daysOfWeek.indexOf(day);\n        // When the selected slot is 23:30-00:00 we roll over to the following day\n        const nextDayName = hours === 23 ? daysOfWeek[(currentDayIndex + 1) % daysOfWeek.length] : day;\n        return isSlotAvailableAndFuture(nextDayName, nextHourFirstHalf);\n      }\n      return false;\n    } catch (error) {\n      console.error('Error checking full hour availability:', error);\n      return false;\n    }\n  };\n\n  // Handle slot selection from table\n  const handleTableSlotSelect = (day, timeSlotKey) => {\n    // Check if slot is in the past\n    if (isSlotInPast(day, timeSlotKey)) {\n      return; // Don't allow booking past slots\n    }\n    const date = addDays(currentWeekStart, daysOfWeek.indexOf(day));\n    setSelectedSlot({\n      day,\n      timeSlot: timeSlotKey,\n      date,\n      formattedDate: format(date, 'yyyy-MM-dd')\n    });\n\n    // Since we removed full hour buttons, all slots are now 30-minute slots\n    // Default to 25 minutes (half lesson)\n    // Default to 25 minutes; for trial it is mandatory, for others still default but can change\n    setLessonDuration('25');\n    setConfirmDialogOpen(true);\n  };\n\n  // Handle booking confirmation\n  const handleConfirmBooking = async () => {\n    if (!selectedSlot || !currentUser || !token) return;\n    try {\n      const [startTime] = selectedSlot.timeSlot.split('-');\n\n      // Create booking datetime by removing student's timezone offset\n      let bookingDateTime;\n      if (studentProfile && studentProfile.timezone) {\n        // The selected time is already in student's timezone, we need to convert it back to base time\n        // Create the datetime in student's timezone\n        const studentDateTime = moment.tz(`${selectedSlot.formattedDate} ${startTime}:00`, 'YYYY-MM-DD HH:mm:ss', studentProfile.timezone);\n\n        // Convert back to base time (remove timezone offset) for database storage\n        bookingDateTime = studentDateTime.clone().subtract(parseTimezoneOffset(studentProfile.timezone), 'minutes').format('YYYY-MM-DD HH:mm:ss');\n      } else {\n        // Fallback to original behavior if no timezone info\n        bookingDateTime = `${selectedSlot.formattedDate} ${startTime}:00`;\n      }\n      console.log('Sending booking request:', {\n        duration: lessonDuration,\n        timezone: studentProfile === null || studentProfile === void 0 ? void 0 : studentProfile.timezone,\n        originalDateTime: `${selectedSlot.formattedDate} ${startTime}:00`,\n        convertedDateTime: bookingDateTime\n      });\n      const {\n        data\n      } = await axios.post('/bookings', {\n        teacher_id: id,\n        datetime: bookingDateTime,\n        duration: lessonDuration,\n        booking_type: 'regular'\n      });\n      if (data.success) {\n        setBookingData(data.data);\n        // Show persistent success toast\n        toast.success(t('booking.bookingSuccessMessage'), {\n          duration: 8000\n        });\n        setConfirmDialogOpen(false);\n\n        // Fetch the meeting details for this booking\n        try {\n          const meetingsResponse = await axios.get('/meetings/student');\n          if (meetingsResponse.data && Array.isArray(meetingsResponse.data.meetings)) {\n            // Find the meeting that matches this booking's datetime\n            const relatedMeeting = meetingsResponse.data.meetings.find(meeting => new Date(meeting.meeting_date).getTime() === new Date(bookingDateTime).getTime());\n            if (relatedMeeting) {\n              console.log('Found related meeting:', relatedMeeting);\n              setBookingData(prevData => ({\n                ...prevData,\n                meeting: relatedMeeting\n              }));\n            }\n          }\n        } catch (meetingError) {\n          console.error('Error fetching meeting details:', meetingError);\n        }\n      } else {\n        setBookingError(data.message || t('booking.bookingFailed'));\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error booking slot:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) === 'Insufficient balance. Please add funds to your wallet.') {\n        setBookingError(t('booking.insufficientBalance'));\n      } else {\n        var _error$response2, _error$response2$data;\n        setBookingError(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || t('booking.bookingFailed'));\n      }\n    }\n  };\n\n  // Generate weekly calendar\n  const renderWeeklyCalendar = () => {\n    if (!teacher || !teacher.available_hours) return null;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: daysOfWeek.map((day, index) => {\n        const date = addDays(currentWeekStart, index);\n        const dayHours = teacher.available_hours[day] || [];\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            elevation: 3,\n            sx: {\n              height: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  pb: 2,\n                  borderBottom: 1,\n                  borderColor: 'divider',\n                  bgcolor: theme.palette.primary.main,\n                  color: 'white',\n                  py: 1,\n                  borderRadius: '4px 4px 0 0',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: t(`days.${day}`)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: format(date, 'MMM d, yyyy', {\n                    locale: isRtl ? ar : enUS\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this), dayHours.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 1\n                },\n                children: dayHours.map((timeSlot, idx) => /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  color: \"primary\",\n                  onClick: () => handleSelectSlot(day, timeSlot),\n                  startIcon: /*#__PURE__*/_jsxDEV(AccessTimeIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 38\n                  }, this),\n                  sx: {\n                    justifyContent: 'flex-start'\n                  },\n                  children: timeSlot\n                }, idx, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  py: 3\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: t('booking.noAvailableSlots')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 15\n          }, this)\n        }, day, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render booking success message\n  const renderBookingSuccess = () => /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 3,\n    sx: {\n      p: 4,\n      textAlign: 'center',\n      my: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n      color: \"success\",\n      sx: {\n        fontSize: 60,\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: t('booking.bookingSuccessTitle')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      paragraph: true,\n      children: t('booking.bookingSuccessMessage')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 637,\n      columnNumber: 7\n    }, this), (bookingData === null || bookingData === void 0 ? void 0 : bookingData.meeting) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3,\n        mb: 3,\n        p: 3,\n        bgcolor: 'background.paper',\n        borderRadius: 2,\n        boxShadow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        color: \"primary\",\n        children: t('booking.meetingCreated')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        gutterBottom: true,\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [t('meetings.name'), \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 13\n        }, this), \" \", bookingData.meeting.meeting_name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        gutterBottom: true,\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [t('meetings.date'), \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 13\n        }, this), \" \", format(new Date(bookingData.meeting.meeting_date), 'PPpp', {\n          locale: isRtl ? ar : enUS\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        gutterBottom: true,\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [t('meetings.duration'), \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 13\n        }, this), \" \", bookingData.meeting.duration, \" \", t('meetings.minutes')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 652,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 1\n        },\n        children: t('booking.meetingAccessInfo')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3,\n        display: 'flex',\n        justifyContent: 'center',\n        gap: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        component: Link,\n        to: `/student/teacher/${id}`,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 22\n        }, this),\n        children: t('booking.backToTeacher')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        component: Link,\n        to: \"/student/meetings\",\n        color: \"primary\",\n        children: t('booking.viewMyMeetings')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        component: Link,\n        to: \"/student/bookings\",\n        color: \"secondary\",\n        children: t('booking.viewMyBookings')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 632,\n    columnNumber: 5\n  }, this);\n\n  // Confirmation dialog\n  const renderConfirmDialog = () => {\n    var _ref6, _ref7, _teacher$trialLessonP4;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: confirmDialogOpen,\n      onClose: () => setConfirmDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [t('booking.confirmBooking'), /*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"close\",\n          onClick: () => setConfirmDialogOpen(false),\n          sx: {\n            position: 'absolute',\n            right: 8,\n            top: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 693,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedSlot && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [t('booking.teacher'), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this), \" \", teacher === null || teacher === void 0 ? void 0 : teacher.full_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [t('booking.day'), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 15\n            }, this), \" \", t(`days.${selectedSlot.day}`)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [t('booking.date'), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 15\n            }, this), \" \", format(selectedSlot.date, 'PPP', {\n              locale: isRtl ? ar : enUS\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [t('booking.time'), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this), \" \", (() => {\n              if (lessonDuration === '50' && selectedSlot) {\n                // For full hour lessons, show full 60-minute time range from selected slot\n                const [startTime] = selectedSlot.timeSlot.split('-');\n                const [hours, minutes] = startTime.split(':').map(Number);\n\n                // Calculate the end time for full hour (60 minutes)\n                const startMinutes = hours * 60 + minutes;\n                const endMinutes = startMinutes + 60; // Full hour = 60 minutes\n                const endHours = Math.floor(endMinutes / 60);\n                const endMins = endMinutes % 60;\n                const actualEndTime = `${String(endHours).padStart(2, '0')}:${String(endMins).padStart(2, '0')}`;\n                return `${startTime} - ${actualEndTime} (${t('booking.fullLesson')})`;\n              } else {\n                // For 25-minute lessons, show the original slot time\n                return selectedSlot.timeSlot;\n              }\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [t('booking.duration'), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 15\n            }, this), \" \", lessonDuration, \" \", t('meetings.minutes'), lessonDuration === '50' ? ` (${t('booking.fullLesson')})` : ` (${t('booking.halfLesson')})`]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [t('booking.price'), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 15\n            }, this), \" $\", (() => {\n              if (lessonDuration === '50') {\n                return teacher === null || teacher === void 0 ? void 0 : teacher.price_per_lesson;\n              } else {\n                if (isTrialEligible) {\n                  var _ref, _ref2, _teacher$trialLessonP, _ref3, _ref4, _teacher$trialLessonP2, _ref5, _teacher$trialLessonP3;\n                  return ((_ref = (_ref2 = (_teacher$trialLessonP = teacher === null || teacher === void 0 ? void 0 : teacher.trialLessonPrice) !== null && _teacher$trialLessonP !== void 0 ? _teacher$trialLessonP : teacher === null || teacher === void 0 ? void 0 : teacher.trial_lesson_price) !== null && _ref2 !== void 0 ? _ref2 : teacher === null || teacher === void 0 ? void 0 : teacher.triallessonprice) !== null && _ref !== void 0 ? _ref : (teacher === null || teacher === void 0 ? void 0 : teacher.price_per_lesson) / 2).toFixed ? ((_ref3 = (_ref4 = (_teacher$trialLessonP2 = teacher === null || teacher === void 0 ? void 0 : teacher.trialLessonPrice) !== null && _teacher$trialLessonP2 !== void 0 ? _teacher$trialLessonP2 : teacher === null || teacher === void 0 ? void 0 : teacher.trial_lesson_price) !== null && _ref4 !== void 0 ? _ref4 : teacher === null || teacher === void 0 ? void 0 : teacher.triallessonprice) !== null && _ref3 !== void 0 ? _ref3 : (teacher === null || teacher === void 0 ? void 0 : teacher.price_per_lesson) / 2).toFixed(2) : (_ref5 = (_teacher$trialLessonP3 = teacher === null || teacher === void 0 ? void 0 : teacher.trialLessonPrice) !== null && _teacher$trialLessonP3 !== void 0 ? _teacher$trialLessonP3 : teacher === null || teacher === void 0 ? void 0 : teacher.trial_lesson_price) !== null && _ref5 !== void 0 ? _ref5 : teacher === null || teacher === void 0 ? void 0 : teacher.triallessonprice;\n                }\n                return ((teacher === null || teacher === void 0 ? void 0 : teacher.price_per_lesson) / 2).toFixed(2);\n              }\n            })(), \" \", t('common.currency')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            component: \"fieldset\",\n            sx: {\n              mt: 2,\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n              component: \"legend\",\n              children: t('booking.selectDuration')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n              name: \"lesson-duration\",\n              value: lessonDuration,\n              onChange: e => {\n                setLessonDuration(e.target.value);\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"25\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 28\n                }, this),\n                label: `${t('booking.halfLesson')} (25 ${t('meetings.minutes')})`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 17\n              }, this), selectedSlot && canBookFullHour(selectedSlot.day, selectedSlot.timeSlot) && /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"50\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 30\n                }, this),\n                label: `${t('booking.fullLesson')} (50 ${t('meetings.minutes')}) - ${t('booking.consecutiveSlots')}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this), isTrialEligible && lessonDuration === '25' && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: t('booking.trialLessonEligible', {\n              price: (_ref6 = (_ref7 = (_teacher$trialLessonP4 = teacher === null || teacher === void 0 ? void 0 : teacher.trialLessonPrice) !== null && _teacher$trialLessonP4 !== void 0 ? _teacher$trialLessonP4 : teacher === null || teacher === void 0 ? void 0 : teacher.trial_lesson_price) !== null && _ref7 !== void 0 ? _ref7 : teacher === null || teacher === void 0 ? void 0 : teacher.triallessonprice) !== null && _ref6 !== void 0 ? _ref6 : ''\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 15\n          }, this), bookingError && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mt: 2\n            },\n            children: bookingError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 703,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setConfirmDialogOpen(false),\n          children: t('common.cancel')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmBooking,\n          variant: \"contained\",\n          color: \"primary\",\n          autoFocus: true,\n          children: t('booking.confirmAndPay')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 796,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 792,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 692,\n      columnNumber: 5\n    }, this);\n  };\n\n  // Main content\n  const content = /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '50vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 812,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 26\n          }, this),\n          component: Link,\n          to: \"/student/find-teacher\",\n          children: t('teacherDetails.backToSearch')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 818,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 816,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(ProfileCompletionAlert, {\n      exemptPages: ['/student/complete-profile', '/student/dashboard'],\n      children: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            display: 'flex',\n            justifyContent: 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 26\n            }, this),\n            component: Link,\n            to: `/student/teacher/${id}`,\n            children: t('booking.backToTeacher')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 833,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 3,\n          sx: {\n            p: 3,\n            mb: 4,\n            borderRadius: 2,\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              flexWrap: 'wrap',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                component: \"h1\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: [t('booking.bookLessonWith'), \" \", teacher.full_name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  opacity: 0.9\n                },\n                children: [t('booking.pricePerLesson'), \": $\", teacher.price_per_lesson, \" \", t('common.currency')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'right'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: studentProfile !== null && studentProfile !== void 0 && studentProfile.timezone ? moment(formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('dddd, MMMM D, YYYY [at] h:mm A') : format(currentTime, 'PPpp', {\n                  locale: isRtl ? ar : enUS\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 17\n              }, this), (studentProfile === null || studentProfile === void 0 ? void 0 : studentProfile.timezone) && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  opacity: 0.8\n                },\n                children: studentProfile.timezone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 3,\n          sx: {\n            p: 3,\n            mb: 4,\n            borderRadius: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n              color: \"primary\",\n              sx: {\n                mt: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: t('booking.instructions')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: t('booking.instructionsText')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 3,\n          sx: {\n            p: 3,\n            mb: 4,\n            borderRadius: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              flexWrap: 'wrap',\n              gap: 2,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n              color: 'white',\n              p: 3,\n              m: -3,\n              mb: 3,\n              borderRadius: '8px 8px 0 0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 'bold',\n                  mb: 1\n                },\n                children: [\"\\uD83D\\uDCC5 \", t('booking.weekNavigation')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: [t('booking.weekOf'), \" \", format(currentWeekStart, 'MMM d', {\n                  locale: isRtl ? ar : enUS\n                }), \" - \", format(addDays(currentWeekStart, 6), 'MMM d, yyyy', {\n                  locale: isRtl ? ar : enUS\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 906,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: t('booking.previousWeek'),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: goToPreviousWeek,\n                    disabled: isPreviousWeekDisabled(),\n                    sx: {\n                      color: 'white',\n                      bgcolor: 'rgba(255, 255, 255, 0.1)',\n                      '&:hover': {\n                        bgcolor: 'rgba(255, 255, 255, 0.2)'\n                      },\n                      '&:disabled': {\n                        color: 'rgba(255, 255, 255, 0.3)',\n                        bgcolor: 'rgba(255, 255, 255, 0.05)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ChevronLeftIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 928,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: t('booking.nextWeek'),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: goToNextWeek,\n                    disabled: isNextWeekDisabled(),\n                    sx: {\n                      color: 'white',\n                      bgcolor: 'rgba(255, 255, 255, 0.1)',\n                      '&:hover': {\n                        bgcolor: 'rgba(255, 255, 255, 0.2)'\n                      },\n                      '&:disabled': {\n                        color: 'rgba(255, 255, 255, 0.3)',\n                        bgcolor: 'rgba(255, 255, 255, 0.05)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ChevronRightIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 949,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 934,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 889,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 888,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 3,\n          sx: {\n            p: 3,\n            mb: 4,\n            borderRadius: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(CalendarMonthIcon, {\n              color: \"primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 15\n            }, this), t('booking.selectTimeSlot')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 13\n          }, this), teacher.available_hours && (() => {\n            try {\n              const parsedHours = typeof teacher.available_hours === 'string' ? JSON.parse(teacher.available_hours) : teacher.available_hours;\n              return /*#__PURE__*/_jsxDEV(BookableHoursTable, {\n                availableHours: parsedHours,\n                loading: loading,\n                showStats: true,\n                onSlotSelect: handleTableSlotSelect,\n                currentWeekStart: currentWeekStart,\n                daysOfWeek: daysOfWeek,\n                isSlotInPast: isSlotInPast,\n                studentTimezone: studentProfile === null || studentProfile === void 0 ? void 0 : studentProfile.timezone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 19\n              }, this);\n            } catch (error) {\n              console.error('Error parsing available hours:', error);\n              return renderWeeklyCalendar();\n            }\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 11\n        }, this), renderConfirmDialog()]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 830,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 810,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: content\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 996,\n    columnNumber: 10\n  }, this);\n};\n_s(BookingPage, \"HYxt4wip5HXnEyL1zsTjcHvk1jw=\", false, function () {\n  return [useParams, useTranslation, useTheme, useNavigate, useAuth, useMediaQuery];\n});\n_c = BookingPage;\nexport default BookingPage;\nvar _c;\n$RefreshReg$(_c, \"BookingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Link", "useTranslation", "Container", "Paper", "Typography", "Box", "Grid", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "Chip", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Divider", "Avatar", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "useTheme", "useMediaQuery", "IconButton", "<PERSON><PERSON><PERSON>", "RadioGroup", "FormControlLabel", "Radio", "FormControl", "FormLabel", "ArrowBack", "ArrowBackIcon", "CalendarMonth", "CalendarMonthIcon", "AccessTime", "AccessTimeIcon", "CheckCircle", "CheckCircleIcon", "Info", "InfoIcon", "Close", "CloseIcon", "ChevronLeft", "ChevronLeftIcon", "ChevronRight", "ChevronRightIcon", "axios", "useAuth", "Layout", "BookableHoursTable", "ProfileCompletionAlert", "format", "addDays", "startOfWeek", "isSameDay", "addWeeks", "subWeeks", "ar", "enUS", "convertBookingDateTime", "getCurrentTimeInTimezone", "formatDateInStudentTimezone", "convertFromDatabaseTime", "parseTimezoneOffset", "moment", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BookingPage", "_s", "id", "t", "i18n", "theme", "navigate", "currentUser", "token", "isMobile", "breakpoints", "down", "isRtl", "language", "teacher", "<PERSON><PERSON><PERSON><PERSON>", "isTrialEligible", "setIsTrialEligible", "loading", "setLoading", "error", "setError", "selectedSlot", "setSelectedSlot", "confirmDialogOpen", "setConfirmDialogOpen", "bookingError", "setBookingError", "bookingData", "setBookingData", "currentWeekStart", "setCurrentWeekStart", "today", "Date", "weekStartsOn", "lessonDuration", "setLessonDuration", "studentProfile", "setStudentProfile", "currentTime", "setCurrentTime", "daysOfWeek", "goToPreviousWeek", "previousWeek", "lastWeek", "goToNextWeek", "nextWeek", "oneYearAhead", "maxWeek", "isPreviousWeekDisabled", "isNextWeekDisabled", "fetchStudentProfile", "data", "get", "headers", "success", "profile", "console", "fetchTeacherDetails", "hasBookedRes", "has<PERSON>revious", "e", "message", "timeInterval", "setInterval", "clearInterval", "getCurrentTimeInStudentTimezone", "timezone", "currentTimeFormatted", "toISOString", "toDate", "isSlotInPast", "day", "timeSlotKey", "date", "indexOf", "startTime", "split", "hours", "minutes", "map", "Number", "dateStr", "timeStr", "toString", "padStart", "slotDateTime", "now", "slotDateTimeStr", "offsetMinutes", "offsetHours", "sign", "absHours", "Math", "abs", "hours_offset", "floor", "minutes_offset", "round", "momentTimezone", "String", "slotMoment", "utcOffset", "setHours", "isPast", "handleSelectSlot", "timeSlot", "formattedDate", "formattedTime", "getConsecutiveHourBlocks", "daySlots", "length", "blocks", "sortedSlots", "sort", "i", "currentSlot", "nextSlot", "includes", "push", "type", "startSlot", "endSlot", "hour", "slot", "minute", "prevSlot", "isFullHourAvailable", "availableHours", "fullHourBlock", "find", "block", "firstSlotPast", "secondSlotPast", "checkCrossHourAvailability", "nextHourFirstHalf", "currentSlotPast", "nextSlotPast", "checkSecondHalfAvailability", "secondHalfSlot", "secondHalfPast", "hasNextSlot", "canBookSecondHalf", "canBookFullFromSecondHalf", "canBookFullHour", "available_hours", "JSON", "parse", "isSlotAvailableAndFuture", "checkDay", "<PERSON><PERSON><PERSON>", "slots", "nextHour", "nextHourStr", "currentDayIndex", "nextDayName", "handleTableSlotSelect", "handleConfirmBooking", "bookingDateTime", "studentDateTime", "tz", "clone", "subtract", "log", "duration", "originalDateTime", "convertedDateTime", "post", "teacher_id", "datetime", "booking_type", "meetingsResponse", "Array", "isArray", "meetings", "relatedMeeting", "meeting", "meeting_date", "getTime", "prevData", "meetingError", "_error$response", "_error$response$data", "response", "_error$response2", "_error$response2$data", "renderWeeklyCalendar", "container", "spacing", "children", "index", "dayHours", "item", "xs", "sm", "md", "lg", "elevation", "sx", "height", "textAlign", "pb", "borderBottom", "borderColor", "bgcolor", "palette", "primary", "main", "color", "py", "borderRadius", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "locale", "display", "flexDirection", "gap", "idx", "onClick", "startIcon", "justifyContent", "renderBookingSuccess", "p", "my", "fontSize", "gutterBottom", "paragraph", "mt", "boxShadow", "meeting_name", "component", "to", "renderConfirmDialog", "_ref6", "_ref7", "_teacher$trialLessonP4", "open", "onClose", "position", "right", "top", "full_name", "startMinutes", "endMinutes", "endHours", "endMins", "actualEndTime", "price_per_lesson", "_ref", "_ref2", "_teacher$trialLessonP", "_ref3", "_ref4", "_teacher$trialLessonP2", "_ref5", "_teacher$trialLessonP3", "trialLessonPrice", "trial_lesson_price", "triallessonprice", "toFixed", "name", "value", "onChange", "target", "control", "label", "severity", "price", "autoFocus", "content", "max<PERSON><PERSON><PERSON>", "alignItems", "minHeight", "exemptPages", "flexWrap", "fontWeight", "opacity", "background", "dark", "m", "title", "disabled", "parsedHours", "showStats", "onSlotSelect", "studentTimezone", "_c", "$RefreshReg$"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/student/BookingPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Container,\n  Paper,\n  Typography,\n  Box,\n  Grid,\n  Button,\n  CircularProgress,\n  Alert,\n  Chip,\n  Card,\n  CardContent,\n  CardActions,\n  Divider,\n  Avatar,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  useTheme,\n  useMediaQuery,\n  IconButton,\n  Tooltip,\n  RadioGroup,\n  FormControlLabel,\n  Radio,\n  FormControl,\n  FormLabel\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  CalendarMonth as CalendarMonthIcon,\n  AccessTime as AccessTimeIcon,\n  CheckCircle as CheckCircleIcon,\n  Info as InfoIcon,\n  Close as CloseIcon,\n  ChevronLeft as ChevronLeftIcon,\n  ChevronRight as ChevronRightIcon\n} from '@mui/icons-material';\nimport axios from '../../utils/axios';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Layout from '../../components/Layout';\nimport BookableHoursTable from '../../components/BookableHoursTable';\nimport ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';\nimport { format, addDays, startOfWeek, isSameDay, addWeeks, subWeeks } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport { convertBookingDateTime, getCurrentTimeInTimezone, formatDateInStudentTimezone, convertFromDatabaseTime, parseTimezoneOffset } from '../../utils/timezone';\nimport moment from 'moment-timezone';\nimport { toast } from 'react-hot-toast';\n\nconst BookingPage = () => {\n  const { id } = useParams();\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { currentUser, token } = useAuth();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const isRtl = i18n.language === 'ar';\n\n  const [teacher, setTeacher] = useState(null);\n  const [isTrialEligible, setIsTrialEligible] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedSlot, setSelectedSlot] = useState(null);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  \n  const [bookingError, setBookingError] = useState(null);\n  const [bookingData, setBookingData] = useState(null);\n  const [currentWeekStart, setCurrentWeekStart] = useState(() => {\n    const today = new Date();\n    return startOfWeek(today, { weekStartsOn: 1 }); // Start from current week\n  });\n  const [lessonDuration, setLessonDuration] = useState('25'); // Default to half lesson (25 minutes)\n  const [studentProfile, setStudentProfile] = useState(null);\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  // Days of the week\n  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n\n  // Week navigation functions\n  const goToPreviousWeek = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const lastWeek = startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 });\n\n    if (previousWeek >= lastWeek) {\n      setCurrentWeekStart(previousWeek);\n    }\n  };\n\n  const goToNextWeek = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n\n    // Don't allow going beyond one year ahead\n    if (nextWeek <= maxWeek) {\n      setCurrentWeekStart(nextWeek);\n    }\n  };\n\n  // Check if navigation buttons should be disabled\n  const isPreviousWeekDisabled = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const lastWeek = startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 });\n    return previousWeek < lastWeek;\n  };\n\n  const isNextWeekDisabled = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n    return nextWeek > maxWeek;\n  };\n\n  // Fetch student profile\n  useEffect(() => {\n    const fetchStudentProfile = async () => {\n      if (!token) return;\n\n      try {\n        const { data } = await axios.get('/api/students/profile', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success && data.profile) {\n          setStudentProfile(data.profile);\n        }\n      } catch (error) {\n        console.error('Error fetching student profile:', error);\n      }\n    };\n\n    fetchStudentProfile();\n  }, [token]);\n\n  // Fetch teacher details\n  useEffect(() => {\n    const fetchTeacherDetails = async () => {\n      if (!id || !token) return;\n\n      try {\n        setLoading(true);\n        const { data } = await axios.get(`/teachers/${id}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success) {\n          setTeacher(data.data);\n      // After teacher details load, check if this is the first booking between student & teacher\n      try {\n        const hasBookedRes = await axios.get(`/api/bookings/student/hasBooked/${id}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (hasBookedRes.data.success) {\n          setIsTrialEligible(!hasBookedRes.data.hasPrevious);\n        }\n      } catch(e) {\n        console.error('Error checking previous bookings', e);\n      }\n        } else {\n          setError(data.message || t('teacherDetails.errorFetching'));\n        }\n      } catch (error) {\n        console.error('Error fetching teacher details:', error);\n        setError(t('teacherDetails.errorFetching'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTeacherDetails();\n  }, [id, token, t]);\n\n  // Update current time every second\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Get current time in student's timezone (same method as meetings page)\n  const getCurrentTimeInStudentTimezone = () => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return new Date();\n    }\n    // Use the same method as the time display at the top\n    const currentTimeFormatted = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    return moment(currentTimeFormatted, 'YYYY-MM-DD HH:mm:ss').toDate();\n  };\n\n  // Check if a time slot is in the past\n  const isSlotInPast = (day, timeSlotKey) => {\n    const date = addDays(currentWeekStart, daysOfWeek.indexOf(day));\n    const [startTime] = timeSlotKey.split('-');\n    const [hours, minutes] = startTime.split(':').map(Number);\n\n    // Create slot datetime in student's timezone\n    const dateStr = format(date, 'yyyy-MM-dd');\n    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n\n    // Create the slot datetime and current time using the same method as the time display\n    let slotDateTime;\n    let now;\n\n    if (studentProfile && studentProfile.timezone) {\n      // Create slot datetime in student's timezone using moment-timezone\n      const slotDateTimeStr = `${dateStr} ${timeStr}:00`;\n\n      // Parse the slot time as if it's in the student's timezone\n      const offsetMinutes = parseTimezoneOffset(studentProfile.timezone);\n      const offsetHours = offsetMinutes / 60;\n      const sign = offsetHours >= 0 ? '+' : '-';\n      const absHours = Math.abs(offsetHours);\n      const hours_offset = Math.floor(absHours);\n      const minutes_offset = Math.round((absHours - hours_offset) * 60);\n      const momentTimezone = `${sign}${String(hours_offset).padStart(2, '0')}:${String(minutes_offset).padStart(2, '0')}`;\n\n      // Create slot datetime in student's timezone\n      const slotMoment = moment(slotDateTimeStr, 'YYYY-MM-DD HH:mm:ss').utcOffset(momentTimezone);\n      slotDateTime = slotMoment.toDate();\n\n      // Get current time in student's timezone using the same method as display\n      const currentTimeFormatted = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n      now = moment(currentTimeFormatted, 'YYYY-MM-DD HH:mm:ss').toDate();\n    } else {\n      // Fallback to local time\n      slotDateTime = new Date(date);\n      slotDateTime.setHours(hours, minutes, 0, 0);\n      now = new Date();\n    }\n\n    const isPast = slotDateTime < now;\n\n    return isPast;\n  };\n\n  // Handle slot selection\n  const handleSelectSlot = (day, timeSlot) => {\n    const date = addDays(currentWeekStart, daysOfWeek.indexOf(day));\n\n    setSelectedSlot({\n      day,\n      timeSlot,\n      date,\n      formattedDate: format(date, 'yyyy-MM-dd'),\n      formattedTime: timeSlot\n    });\n\n    setConfirmDialogOpen(true);\n  };\n\n  // Get consecutive hour blocks for a day\n  const getConsecutiveHourBlocks = (daySlots) => {\n    if (!daySlots || daySlots.length === 0) return [];\n\n    const blocks = [];\n    const sortedSlots = [...daySlots].sort();\n\n    for (let i = 0; i < sortedSlots.length; i++) {\n      const currentSlot = sortedSlots[i];\n      const [startTime] = currentSlot.split('-');\n      const [hours, minutes] = startTime.split(':').map(Number);\n\n      // Only process slots that start at :00 (beginning of hour)\n      if (minutes === 0) {\n        // Check if the next 30-minute slot is also available\n        let nextSlot;\n        if (hours === 23) {\n          // Special case for 23:00 - next slot is 23:30-00:00\n          nextSlot = `23:30-00:00`;\n        } else {\n          nextSlot = `${hours.toString().padStart(2, '0')}:30-${(hours + 1).toString().padStart(2, '0')}:00`;\n        }\n\n        if (sortedSlots.includes(nextSlot)) {\n          // This is a full hour block\n          blocks.push({\n            type: 'full',\n            startSlot: currentSlot,\n            endSlot: nextSlot,\n            hour: hours\n          });\n          // Skip the next slot since it's part of this full hour\n          i++;\n        } else {\n          // This is just a half hour\n          blocks.push({\n            type: 'half',\n            slot: currentSlot,\n            hour: hours,\n            minute: minutes\n          });\n        }\n      } else {\n        // This is a :30 slot, check if it's not part of a full hour we already processed\n        const prevSlot = `${hours.toString().padStart(2, '0')}:00-${hours.toString().padStart(2, '0')}:30`;\n        if (!sortedSlots.includes(prevSlot)) {\n          // This is just a half hour\n          blocks.push({\n            type: 'half',\n            slot: currentSlot,\n            hour: hours,\n            minute: minutes\n          });\n        }\n      }\n    }\n\n    return blocks;\n  };\n\n  // Check if a full hour is available for the selected slot\n  const isFullHourAvailable = (day, timeSlotKey, availableHours) => {\n    if (!availableHours || !availableHours[day]) return false;\n\n    const blocks = getConsecutiveHourBlocks(availableHours[day]);\n    const fullHourBlock = blocks.find(block =>\n      block.type === 'full' && (block.startSlot === timeSlotKey || block.endSlot === timeSlotKey)\n    );\n\n    if (!fullHourBlock) return false;\n\n    // Check if both slots are available (not in the past)\n    const firstSlotPast = isSlotInPast(day, fullHourBlock.startSlot);\n    const secondSlotPast = isSlotInPast(day, fullHourBlock.endSlot);\n\n    // Full hour is only available if both slots are not in the past\n    return !firstSlotPast && !secondSlotPast;\n  };\n\n  // Check if cross-hour booking is available\n  const checkCrossHourAvailability = (day, timeSlotKey, availableHours) => {\n    if (!availableHours || !availableHours[day]) return false;\n\n    const [startTime] = timeSlotKey.split('-');\n    const [hours, minutes] = startTime.split(':').map(Number);\n\n    // Cross-hour booking is only possible if this is a :30 slot\n    if (minutes !== 30) return false;\n\n    // Check if the next hour's first half is available\n    const nextHourFirstHalf = `${((hours + 1) % 24).toString().padStart(2, '0')}:00-${((hours + 1) % 24).toString().padStart(2, '0')}:30`;\n\n    // Check if next hour slot exists in available hours\n    if (!availableHours[day].includes(nextHourFirstHalf)) return false;\n\n    // Check if both slots are not in the past\n    const currentSlotPast = isSlotInPast(day, timeSlotKey);\n    const nextSlotPast = isSlotInPast(day, nextHourFirstHalf);\n\n    return !currentSlotPast && !nextSlotPast;\n  };\n\n  // Check if second half of hour is available for booking with flexible options\n  const checkSecondHalfAvailability = (day, timeSlotKey, availableHours) => {\n    if (!availableHours || !availableHours[day]) return false;\n\n    const [startTime] = timeSlotKey.split('-');\n    const [hours, minutes] = startTime.split(':').map(Number);\n\n    // For :00 slots, check if the second half (:30) is available\n    if (minutes === 0) {\n      const secondHalfSlot = `${hours.toString().padStart(2, '0')}:30-${((hours + 1) % 24).toString().padStart(2, '0')}:00`;\n\n      // Check if second half slot exists and is not in the past\n      if (availableHours[day].includes(secondHalfSlot)) {\n        const secondHalfPast = isSlotInPast(day, secondHalfSlot);\n        if (!secondHalfPast) {\n          // Check if the slot after the second half is also available (for full lesson option)\n          const nextHourFirstHalf = `${((hours + 1) % 24).toString().padStart(2, '0')}:00-${((hours + 1) % 24).toString().padStart(2, '0')}:30`;\n          const hasNextSlot = availableHours[day].includes(nextHourFirstHalf) && !isSlotInPast(day, nextHourFirstHalf);\n\n          return {\n            canBookSecondHalf: true,\n            canBookFullFromSecondHalf: hasNextSlot\n          };\n        }\n      }\n    }\n\n    // For :30 slots, check if the next hour's first half is available (for full lesson option)\n    if (minutes === 30) {\n      const nextHourFirstHalf = `${((hours + 1) % 24).toString().padStart(2, '0')}:00-${((hours + 1) % 24).toString().padStart(2, '0')}:30`;\n      const hasNextSlot = availableHours[day].includes(nextHourFirstHalf) && !isSlotInPast(day, nextHourFirstHalf);\n\n      return {\n        canBookSecondHalf: true,\n        canBookFullFromSecondHalf: hasNextSlot\n      };\n    }\n\n    return {\n      canBookSecondHalf: false,\n      canBookFullFromSecondHalf: false\n    };\n  };\n\n\n\n  // Check if full hour lesson is possible for selected slot (supports cross-day)\n  const canBookFullHour = (day, timeSlotKey) => {\n    if (!teacher?.available_hours) return false;\n\n    try {\n      const availableHours = typeof teacher.available_hours === 'string'\n        ? JSON.parse(teacher.available_hours)\n        : teacher.available_hours;\n\n      // Extract hour/minute of the chosen slot\n      const [startTime] = timeSlotKey.split('-');\n      const [hours, minutes] = startTime.split(':').map(Number);\n\n      // Helper – make sure the given slot is available on the given day and not in the past\n      const isSlotAvailableAndFuture = (checkDay, slotKey) => {\n        const slots = availableHours[checkDay] || [];\n        return slots.includes(slotKey) && !isSlotInPast(checkDay, slotKey);\n      };\n\n      // If user picked the first half of an hour (:00-:30), we only need the second half on the SAME day\n      if (minutes === 0) {\n        const secondHalfSlot = `${hours.toString().padStart(2, '0')}:30-${((hours + 1) % 24).toString().padStart(2, '0')}:00`;\n        return isSlotAvailableAndFuture(day, secondHalfSlot);\n      }\n\n      // If user picked the second half of an hour (:30-:00), we need the next hour's first half\n      if (minutes === 30) {\n        // Calculate next hour and whether this crosses to the next calendar day\n        const nextHour = (hours + 1) % 24;\n        const nextHourStr = nextHour.toString().padStart(2, '0');\n        const nextHourFirstHalf = `${nextHourStr}:00-${nextHourStr}:30`;\n\n        const currentDayIndex = daysOfWeek.indexOf(day);\n        // When the selected slot is 23:30-00:00 we roll over to the following day\n        const nextDayName = hours === 23\n          ? daysOfWeek[(currentDayIndex + 1) % daysOfWeek.length]\n          : day;\n\n        return isSlotAvailableAndFuture(nextDayName, nextHourFirstHalf);\n      }\n\n      return false;\n    } catch (error) {\n      console.error('Error checking full hour availability:', error);\n      return false;\n    }\n  };\n\n  // Handle slot selection from table\n  const handleTableSlotSelect = (day, timeSlotKey) => {\n    // Check if slot is in the past\n    if (isSlotInPast(day, timeSlotKey)) {\n      return; // Don't allow booking past slots\n    }\n\n    const date = addDays(currentWeekStart, daysOfWeek.indexOf(day));\n\n    setSelectedSlot({\n      day,\n      timeSlot: timeSlotKey,\n      date,\n      formattedDate: format(date, 'yyyy-MM-dd')\n    });\n\n    // Since we removed full hour buttons, all slots are now 30-minute slots\n    // Default to 25 minutes (half lesson)\n    // Default to 25 minutes; for trial it is mandatory, for others still default but can change\n  setLessonDuration('25');\n\n    setConfirmDialogOpen(true);\n  };\n\n  // Handle booking confirmation\n  const handleConfirmBooking = async () => {\n    if (!selectedSlot || !currentUser || !token) return;\n\n    try {\n      const [startTime] = selectedSlot.timeSlot.split('-');\n\n      // Create booking datetime by removing student's timezone offset\n      let bookingDateTime;\n      if (studentProfile && studentProfile.timezone) {\n        // The selected time is already in student's timezone, we need to convert it back to base time\n        // Create the datetime in student's timezone\n        const studentDateTime = moment.tz(\n          `${selectedSlot.formattedDate} ${startTime}:00`,\n          'YYYY-MM-DD HH:mm:ss',\n          studentProfile.timezone\n        );\n\n        // Convert back to base time (remove timezone offset) for database storage\n        bookingDateTime = studentDateTime.clone().subtract(parseTimezoneOffset(studentProfile.timezone), 'minutes').format('YYYY-MM-DD HH:mm:ss');\n      } else {\n        // Fallback to original behavior if no timezone info\n        bookingDateTime = `${selectedSlot.formattedDate} ${startTime}:00`;\n      }\n\n      console.log('Sending booking request:', {\n        duration: lessonDuration,\n        timezone: studentProfile?.timezone,\n        originalDateTime: `${selectedSlot.formattedDate} ${startTime}:00`,\n        convertedDateTime: bookingDateTime\n      });\n\n      const { data } = await axios.post('/bookings', {\n        teacher_id: id,\n        datetime: bookingDateTime,\n        duration: lessonDuration,\n        booking_type: 'regular'\n      });\n\n      if (data.success) {\n        \n        setBookingData(data.data);\n      // Show persistent success toast\n      toast.success(t('booking.bookingSuccessMessage'), { duration: 8000 });\n        setConfirmDialogOpen(false);\n\n        // Fetch the meeting details for this booking\n        try {\n          const meetingsResponse = await axios.get('/meetings/student');\n          if (meetingsResponse.data && Array.isArray(meetingsResponse.data.meetings)) {\n            // Find the meeting that matches this booking's datetime\n            const relatedMeeting = meetingsResponse.data.meetings.find(\n              meeting => new Date(meeting.meeting_date).getTime() === new Date(bookingDateTime).getTime()\n            );\n\n            if (relatedMeeting) {\n              console.log('Found related meeting:', relatedMeeting);\n              setBookingData(prevData => ({\n                ...prevData,\n                meeting: relatedMeeting\n              }));\n            }\n          }\n        } catch (meetingError) {\n          console.error('Error fetching meeting details:', meetingError);\n        }\n      } else {\n        setBookingError(data.message || t('booking.bookingFailed'));\n      }\n    } catch (error) {\n      console.error('Error booking slot:', error);\n      if (error.response?.data?.message === 'Insufficient balance. Please add funds to your wallet.') {\n        setBookingError(t('booking.insufficientBalance'));\n      } else {\n        setBookingError(error.response?.data?.message || t('booking.bookingFailed'));\n      }\n    }\n  };\n\n  // Generate weekly calendar\n  const renderWeeklyCalendar = () => {\n    if (!teacher || !teacher.available_hours) return null;\n\n    return (\n      <Grid container spacing={2}>\n        {daysOfWeek.map((day, index) => {\n          const date = addDays(currentWeekStart, index);\n          const dayHours = teacher.available_hours[day] || [];\n\n          return (\n            <Grid item xs={12} sm={6} md={4} lg={3} key={day}>\n              <Card elevation={3} sx={{ height: '100%' }}>\n                <CardContent>\n                  <Box sx={{\n                    textAlign: 'center',\n                    pb: 2,\n                    borderBottom: 1,\n                    borderColor: 'divider',\n                    bgcolor: theme.palette.primary.main,\n                    color: 'white',\n                    py: 1,\n                    borderRadius: '4px 4px 0 0',\n                    mb: 2\n                  }}>\n                    <Typography variant=\"h6\">\n                      {t(`days.${day}`)}\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {format(date, 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                    </Typography>\n                  </Box>\n\n                  {dayHours.length > 0 ? (\n                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n                      {dayHours.map((timeSlot, idx) => (\n                        <Button\n                          key={idx}\n                          variant=\"outlined\"\n                          color=\"primary\"\n                          onClick={() => handleSelectSlot(day, timeSlot)}\n                          startIcon={<AccessTimeIcon />}\n                          sx={{ justifyContent: 'flex-start' }}\n                        >\n                          {timeSlot}\n                        </Button>\n                      ))}\n                    </Box>\n                  ) : (\n                    <Box sx={{ textAlign: 'center', py: 3 }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        {t('booking.noAvailableSlots')}\n                      </Typography>\n                    </Box>\n                  )}\n                </CardContent>\n              </Card>\n            </Grid>\n          );\n        })}\n      </Grid>\n    );\n  };\n\n  // Render booking success message\n  const renderBookingSuccess = () => (\n    <Paper elevation={3} sx={{ p: 4, textAlign: 'center', my: 4 }}>\n      <CheckCircleIcon color=\"success\" sx={{ fontSize: 60, mb: 2 }} />\n      <Typography variant=\"h5\" gutterBottom>\n        {t('booking.bookingSuccessTitle')}\n      </Typography>\n      <Typography variant=\"body1\" paragraph>\n        {t('booking.bookingSuccessMessage')}\n      </Typography>\n\n      {bookingData?.meeting && (\n        <Box sx={{ mt: 3, mb: 3, p: 3, bgcolor: 'background.paper', borderRadius: 2, boxShadow: 1 }}>\n          <Typography variant=\"h6\" gutterBottom color=\"primary\">\n            {t('booking.meetingCreated')}\n          </Typography>\n          <Typography variant=\"body1\" gutterBottom>\n            <strong>{t('meetings.name')}:</strong> {bookingData.meeting.meeting_name}\n          </Typography>\n          <Typography variant=\"body1\" gutterBottom>\n            <strong>{t('meetings.date')}:</strong> {format(new Date(bookingData.meeting.meeting_date), 'PPpp', { locale: isRtl ? ar : enUS })}\n          </Typography>\n          <Typography variant=\"body1\" gutterBottom>\n            <strong>{t('meetings.duration')}:</strong> {bookingData.meeting.duration} {t('meetings.minutes')}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n            {t('booking.meetingAccessInfo')}\n          </Typography>\n        </Box>\n      )}\n\n      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>\n        <Button\n          variant=\"outlined\"\n          component={Link}\n          to={`/student/teacher/${id}`}\n          startIcon={<ArrowBackIcon />}\n        >\n          {t('booking.backToTeacher')}\n        </Button>\n        <Button\n          variant=\"contained\"\n          component={Link}\n          to=\"/student/meetings\"\n          color=\"primary\"\n        >\n          {t('booking.viewMyMeetings')}\n        </Button>\n        <Button\n          variant=\"outlined\"\n          component={Link}\n          to=\"/student/bookings\"\n          color=\"secondary\"\n        >\n          {t('booking.viewMyBookings')}\n        </Button>\n      </Box>\n    </Paper>\n  );\n\n  // Confirmation dialog\n  const renderConfirmDialog = () => (\n    <Dialog open={confirmDialogOpen} onClose={() => setConfirmDialogOpen(false)}>\n      <DialogTitle>\n        {t('booking.confirmBooking')}\n        <IconButton\n          aria-label=\"close\"\n          onClick={() => setConfirmDialogOpen(false)}\n          sx={{ position: 'absolute', right: 8, top: 8 }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n      <DialogContent>\n        {selectedSlot && (\n          <Box sx={{ py: 2 }}>\n            <Typography variant=\"body1\" gutterBottom>\n              <strong>{t('booking.teacher')}:</strong> {teacher?.full_name}\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              <strong>{t('booking.day')}:</strong> {t(`days.${selectedSlot.day}`)}\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              <strong>{t('booking.date')}:</strong> {format(selectedSlot.date, 'PPP', { locale: isRtl ? ar : enUS })}\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              <strong>{t('booking.time')}:</strong> {(() => {\n                if (lessonDuration === '50' && selectedSlot) {\n                  // For full hour lessons, show full 60-minute time range from selected slot\n                  const [startTime] = selectedSlot.timeSlot.split('-');\n                  const [hours, minutes] = startTime.split(':').map(Number);\n\n                  // Calculate the end time for full hour (60 minutes)\n                  const startMinutes = hours * 60 + minutes;\n                  const endMinutes = startMinutes + 60; // Full hour = 60 minutes\n                  const endHours = Math.floor(endMinutes / 60);\n                  const endMins = endMinutes % 60;\n\n                  const actualEndTime = `${String(endHours).padStart(2, '0')}:${String(endMins).padStart(2, '0')}`;\n\n                  return `${startTime} - ${actualEndTime} (${t('booking.fullLesson')})`;\n                } else {\n                  // For 25-minute lessons, show the original slot time\n                  return selectedSlot.timeSlot;\n                }\n              })()}\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              <strong>{t('booking.duration')}:</strong> {lessonDuration} {t('meetings.minutes')}\n              {lessonDuration === '50' ? ` (${t('booking.fullLesson')})` : ` (${t('booking.halfLesson')})`}\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              <strong>{t('booking.price')}:</strong> ${(() => {\n                if (lessonDuration === '50') {\n                  return teacher?.price_per_lesson;\n                } else {\n                  if (isTrialEligible) {\n            return (teacher?.trialLessonPrice ?? teacher?.trial_lesson_price ?? teacher?.triallessonprice ?? (teacher?.price_per_lesson / 2)).toFixed ? (teacher?.trialLessonPrice ?? teacher?.trial_lesson_price ?? teacher?.triallessonprice ?? (teacher?.price_per_lesson / 2)).toFixed(2) : (teacher?.trialLessonPrice ?? teacher?.trial_lesson_price ?? teacher?.triallessonprice);\n          }\n          return (teacher?.price_per_lesson / 2).toFixed(2);\n                }\n              })()} {t('common.currency')}\n            </Typography>\n\n            <FormControl component=\"fieldset\" sx={{ mt: 2, mb: 2 }}>\n              <FormLabel component=\"legend\">{t('booking.selectDuration')}</FormLabel>\n              <RadioGroup\n                name=\"lesson-duration\"\n                value={lessonDuration}\n                onChange={(e) => {\n                  setLessonDuration(e.target.value);\n                }}\n              >\n                <FormControlLabel\n                  value=\"25\"\n                  control={<Radio />}\n                  label={`${t('booking.halfLesson')} (25 ${t('meetings.minutes')})`}\n                />\n                {selectedSlot && canBookFullHour(selectedSlot.day, selectedSlot.timeSlot) && (\n                  <FormControlLabel\n                    value=\"50\"\n                    control={<Radio />}\n                    label={`${t('booking.fullLesson')} (50 ${t('meetings.minutes')}) - ${t('booking.consecutiveSlots')}`}\n                  />\n                )}\n              </RadioGroup>\n            </FormControl>\n\n            {isTrialEligible && lessonDuration === '25' && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                {t('booking.trialLessonEligible', { price: teacher?.trialLessonPrice ?? teacher?.trial_lesson_price ?? teacher?.triallessonprice ?? '' })}\n              </Alert>\n            )}\n\n          {bookingError && (\n              <Alert severity=\"error\" sx={{ mt: 2 }}>\n                {bookingError}\n              </Alert>\n            )}\n          </Box>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={() => setConfirmDialogOpen(false)}>\n          {t('common.cancel')}\n        </Button>\n        <Button\n          onClick={handleConfirmBooking}\n          variant=\"contained\"\n          color=\"primary\"\n          autoFocus\n        >\n          {t('booking.confirmAndPay')}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  // Main content\n  const content = (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      {loading ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>\n          <CircularProgress />\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\">{error}</Alert>\n          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>\n            <Button\n              variant=\"contained\"\n              startIcon={<ArrowBackIcon />}\n              component={Link}\n              to=\"/student/find-teacher\"\n            >\n              {t('teacherDetails.backToSearch')}\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>\n        <>\n          {/* Back Button */}\n          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-start' }}>\n            <Button\n              variant=\"outlined\"\n              startIcon={<ArrowBackIcon />}\n              component={Link}\n              to={`/student/teacher/${id}`}\n            >\n              {t('booking.backToTeacher')}\n            </Button>\n          </Box>\n\n          {/* Current Time Display */}\n          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2, bgcolor: 'primary.main', color: 'white' }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>\n              <Box>\n                <Typography variant=\"h5\" component=\"h1\" sx={{ fontWeight: 'bold' }}>\n                  {t('booking.bookLessonWith')} {teacher.full_name}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                  {t('booking.pricePerLesson')}: ${teacher.price_per_lesson} {t('common.currency')}\n                </Typography>\n              </Box>\n              <Box sx={{ textAlign: 'right' }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                  {studentProfile?.timezone ? (\n                    moment(formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('dddd, MMMM D, YYYY [at] h:mm A')\n                  ) : (\n                    format(currentTime, 'PPpp', {\n                      locale: isRtl ? ar : enUS\n                    })\n                  )}\n                </Typography>\n                {studentProfile?.timezone && (\n                  <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                    {studentProfile.timezone}\n                  </Typography>\n                )}\n              </Box>\n            </Box>\n          </Paper>\n\n          {/* Booking Instructions */}\n          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\n              <InfoIcon color=\"primary\" sx={{ mt: 0.5 }} />\n              <Typography variant=\"h6\" gutterBottom>\n                {t('booking.instructions')}\n              </Typography>\n            </Box>\n            <Typography variant=\"body1\" paragraph>\n              {t('booking.instructionsText')}\n            </Typography>\n          </Paper>\n\n          {/* Week Navigation */}\n          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>\n            <Box sx={{\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              flexWrap: 'wrap',\n              gap: 2,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n              color: 'white',\n              p: 3,\n              m: -3,\n              mb: 3,\n              borderRadius: '8px 8px 0 0'\n            }}>\n              <Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                  📅 {t('booking.weekNavigation')}\n                </Typography>\n                <Typography variant=\"h5\" sx={{ fontWeight: 'bold' }}>\n                  {t('booking.weekOf')} {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                </Typography>\n              </Box>\n              <Box sx={{ display: 'flex', gap: 1 }}>\n                <Tooltip title={t('booking.previousWeek')}>\n                  <span>\n                    <IconButton\n                      onClick={goToPreviousWeek}\n                      disabled={isPreviousWeekDisabled()}\n                      sx={{\n                        color: 'white',\n                        bgcolor: 'rgba(255, 255, 255, 0.1)',\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 255, 255, 0.2)',\n                        },\n                        '&:disabled': {\n                          color: 'rgba(255, 255, 255, 0.3)',\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        }\n                      }}\n                    >\n                      <ChevronLeftIcon />\n                    </IconButton>\n                  </span>\n                </Tooltip>\n                <Tooltip title={t('booking.nextWeek')}>\n                  <span>\n                    <IconButton\n                      onClick={goToNextWeek}\n                      disabled={isNextWeekDisabled()}\n                      sx={{\n                        color: 'white',\n                        bgcolor: 'rgba(255, 255, 255, 0.1)',\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 255, 255, 0.2)',\n                        },\n                        '&:disabled': {\n                          color: 'rgba(255, 255, 255, 0.3)',\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        }\n                      }}\n                    >\n                      <ChevronRightIcon />\n                    </IconButton>\n                  </span>\n                </Tooltip>\n              </Box>\n            </Box>\n          </Paper>\n\n          {/* Weekly Calendar */}\n          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>\n            <Typography variant=\"h5\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>\n              <CalendarMonthIcon color=\"primary\" />\n              {t('booking.selectTimeSlot')}\n            </Typography>\n\n            {teacher.available_hours && (() => {\n              try {\n                const parsedHours = typeof teacher.available_hours === 'string'\n                  ? JSON.parse(teacher.available_hours)\n                  : teacher.available_hours;\n                return (\n                  <BookableHoursTable\n                    availableHours={parsedHours}\n                    loading={loading}\n                    showStats={true}\n                    onSlotSelect={handleTableSlotSelect}\n                    currentWeekStart={currentWeekStart}\n                    daysOfWeek={daysOfWeek}\n                    isSlotInPast={isSlotInPast}\n                    studentTimezone={studentProfile?.timezone}\n                  />\n                );\n              } catch (error) {\n                console.error('Error parsing available hours:', error);\n                return renderWeeklyCalendar();\n              }\n            })()}\n          </Paper>\n\n          {/* Confirmation Dialog */}\n          {renderConfirmDialog()}\n        </>\n        </ProfileCompletionAlert>\n      )}\n    </Container>\n  );\n\n  return <Layout>{content}</Layout>;\n};\n\nexport default BookingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SACEC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,WAAW,EACXC,SAAS,QACJ,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,aAAa,IAAIC,iBAAiB,EAClCC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,eAAe,EAC9BC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,mBAAmB;AACrC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,sBAAsB,MAAM,iDAAiD;AACpF,SAASC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,UAAU;AACtF,SAASC,EAAE,EAAEC,IAAI,QAAQ,iBAAiB;AAC1C,SAASC,sBAAsB,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,uBAAuB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAClK,OAAOC,MAAM,MAAM,iBAAiB;AACpC,SAASC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAG,CAAC,GAAGzE,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAE0E,CAAC;IAAEC;EAAK,CAAC,GAAGxE,cAAc,CAAC,CAAC;EACpC,MAAMyE,KAAK,GAAGtD,QAAQ,CAAC,CAAC;EACxB,MAAMuD,QAAQ,GAAG5E,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6E,WAAW;IAAEC;EAAM,CAAC,GAAG/B,OAAO,CAAC,CAAC;EACxC,MAAMgC,QAAQ,GAAGzD,aAAa,CAACqD,KAAK,CAACK,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAMC,KAAK,GAAGR,IAAI,CAACS,QAAQ,KAAK,IAAI;EAEpC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyF,eAAe,EAAEC,kBAAkB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2F,OAAO,EAAEC,UAAU,CAAC,GAAG5F,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6F,KAAK,EAAEC,QAAQ,CAAC,GAAG9F,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+F,YAAY,EAAEC,eAAe,CAAC,GAAGhG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqG,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxG,QAAQ,CAAC,MAAM;IAC7D,MAAMyG,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,OAAOlD,WAAW,CAACiD,KAAK,EAAE;MAAEE,YAAY,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC8G,cAAc,EAAEC,iBAAiB,CAAC,GAAG/G,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgH,WAAW,EAAEC,cAAc,CAAC,GAAGjH,QAAQ,CAAC,IAAI0G,IAAI,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAMQ,UAAU,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;;EAEjG;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,YAAY,GAAGzD,QAAQ,CAAC4C,gBAAgB,EAAE,CAAC,CAAC;IAClD,MAAME,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMW,QAAQ,GAAG7D,WAAW,CAACG,QAAQ,CAAC8C,KAAK,EAAE,CAAC,CAAC,EAAE;MAAEE,YAAY,EAAE;IAAE,CAAC,CAAC;IAErE,IAAIS,YAAY,IAAIC,QAAQ,EAAE;MAC5Bb,mBAAmB,CAACY,YAAY,CAAC;IACnC;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,QAAQ,GAAG7D,QAAQ,CAAC6C,gBAAgB,EAAE,CAAC,CAAC;IAC9C,MAAME,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMc,YAAY,GAAG9D,QAAQ,CAAC+C,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;IAC1C,MAAMgB,OAAO,GAAGjE,WAAW,CAACgE,YAAY,EAAE;MAAEb,YAAY,EAAE;IAAE,CAAC,CAAC;;IAE9D;IACA,IAAIY,QAAQ,IAAIE,OAAO,EAAE;MACvBjB,mBAAmB,CAACe,QAAQ,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMG,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMN,YAAY,GAAGzD,QAAQ,CAAC4C,gBAAgB,EAAE,CAAC,CAAC;IAClD,MAAME,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMW,QAAQ,GAAG7D,WAAW,CAACG,QAAQ,CAAC8C,KAAK,EAAE,CAAC,CAAC,EAAE;MAAEE,YAAY,EAAE;IAAE,CAAC,CAAC;IACrE,OAAOS,YAAY,GAAGC,QAAQ;EAChC,CAAC;EAED,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMJ,QAAQ,GAAG7D,QAAQ,CAAC6C,gBAAgB,EAAE,CAAC,CAAC;IAC9C,MAAME,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMc,YAAY,GAAG9D,QAAQ,CAAC+C,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;IAC1C,MAAMgB,OAAO,GAAGjE,WAAW,CAACgE,YAAY,EAAE;MAAEb,YAAY,EAAE;IAAE,CAAC,CAAC;IAC9D,OAAOY,QAAQ,GAAGE,OAAO;EAC3B,CAAC;;EAED;EACAxH,SAAS,CAAC,MAAM;IACd,MAAM2H,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAI,CAAC3C,KAAK,EAAE;MAEZ,IAAI;QACF,MAAM;UAAE4C;QAAK,CAAC,GAAG,MAAM5E,KAAK,CAAC6E,GAAG,CAAC,uBAAuB,EAAE;UACxDC,OAAO,EAAE;YACP,eAAe,EAAE,UAAU9C,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAI4C,IAAI,CAACG,OAAO,IAAIH,IAAI,CAACI,OAAO,EAAE;UAChClB,iBAAiB,CAACc,IAAI,CAACI,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAOpC,KAAK,EAAE;QACdqC,OAAO,CAACrC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;IACF,CAAC;IAED+B,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC3C,KAAK,CAAC,CAAC;;EAEX;EACAhF,SAAS,CAAC,MAAM;IACd,MAAMkI,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAI,CAACxD,EAAE,IAAI,CAACM,KAAK,EAAE;MAEnB,IAAI;QACFW,UAAU,CAAC,IAAI,CAAC;QAChB,MAAM;UAAEiC;QAAK,CAAC,GAAG,MAAM5E,KAAK,CAAC6E,GAAG,CAAC,aAAanD,EAAE,EAAE,EAAE;UAClDoD,OAAO,EAAE;YACP,eAAe,EAAE,UAAU9C,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAI4C,IAAI,CAACG,OAAO,EAAE;UAChBxC,UAAU,CAACqC,IAAI,CAACA,IAAI,CAAC;UACzB;UACA,IAAI;YACF,MAAMO,YAAY,GAAG,MAAMnF,KAAK,CAAC6E,GAAG,CAAC,mCAAmCnD,EAAE,EAAE,EAAE;cAC5EoD,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU9C,KAAK;cAClC;YACF,CAAC,CAAC;YACF,IAAImD,YAAY,CAACP,IAAI,CAACG,OAAO,EAAE;cAC7BtC,kBAAkB,CAAC,CAAC0C,YAAY,CAACP,IAAI,CAACQ,WAAW,CAAC;YACpD;UACF,CAAC,CAAC,OAAMC,CAAC,EAAE;YACTJ,OAAO,CAACrC,KAAK,CAAC,kCAAkC,EAAEyC,CAAC,CAAC;UACtD;QACE,CAAC,MAAM;UACLxC,QAAQ,CAAC+B,IAAI,CAACU,OAAO,IAAI3D,CAAC,CAAC,8BAA8B,CAAC,CAAC;QAC7D;MACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdqC,OAAO,CAACrC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvDC,QAAQ,CAAClB,CAAC,CAAC,8BAA8B,CAAC,CAAC;MAC7C,CAAC,SAAS;QACRgB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDuC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACxD,EAAE,EAAEM,KAAK,EAAEL,CAAC,CAAC,CAAC;;EAElB;EACA3E,SAAS,CAAC,MAAM;IACd,MAAMuI,YAAY,GAAGC,WAAW,CAAC,MAAM;MACrCxB,cAAc,CAAC,IAAIP,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMgC,aAAa,CAACF,YAAY,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,+BAA+B,GAAGA,CAAA,KAAM;IAC5C,IAAI,CAAC7B,cAAc,IAAI,CAACA,cAAc,CAAC8B,QAAQ,EAAE;MAC/C,OAAO,IAAIlC,IAAI,CAAC,CAAC;IACnB;IACA;IACA,MAAMmC,oBAAoB,GAAG7E,2BAA2B,CAAC,IAAI0C,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC,EAAEhC,cAAc,CAAC8B,QAAQ,EAAE,qBAAqB,CAAC;IAClI,OAAOzE,MAAM,CAAC0E,oBAAoB,EAAE,qBAAqB,CAAC,CAACE,MAAM,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAACC,GAAG,EAAEC,WAAW,KAAK;IACzC,MAAMC,IAAI,GAAG5F,OAAO,CAACgD,gBAAgB,EAAEW,UAAU,CAACkC,OAAO,CAACH,GAAG,CAAC,CAAC;IAC/D,MAAM,CAACI,SAAS,CAAC,GAAGH,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC;IAC1C,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGH,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC;;IAEzD;IACA,MAAMC,OAAO,GAAGrG,MAAM,CAAC6F,IAAI,EAAE,YAAY,CAAC;IAC1C,MAAMS,OAAO,GAAG,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIN,OAAO,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAE7F;IACA,IAAIC,YAAY;IAChB,IAAIC,GAAG;IAEP,IAAIlD,cAAc,IAAIA,cAAc,CAAC8B,QAAQ,EAAE;MAC7C;MACA,MAAMqB,eAAe,GAAG,GAAGN,OAAO,IAAIC,OAAO,KAAK;;MAElD;MACA,MAAMM,aAAa,GAAGhG,mBAAmB,CAAC4C,cAAc,CAAC8B,QAAQ,CAAC;MAClE,MAAMuB,WAAW,GAAGD,aAAa,GAAG,EAAE;MACtC,MAAME,IAAI,GAAGD,WAAW,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;MACzC,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,WAAW,CAAC;MACtC,MAAMK,YAAY,GAAGF,IAAI,CAACG,KAAK,CAACJ,QAAQ,CAAC;MACzC,MAAMK,cAAc,GAAGJ,IAAI,CAACK,KAAK,CAAC,CAACN,QAAQ,GAAGG,YAAY,IAAI,EAAE,CAAC;MACjE,MAAMI,cAAc,GAAG,GAAGR,IAAI,GAAGS,MAAM,CAACL,YAAY,CAAC,CAACV,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIe,MAAM,CAACH,cAAc,CAAC,CAACZ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;MAEnH;MACA,MAAMgB,UAAU,GAAG3G,MAAM,CAAC8F,eAAe,EAAE,qBAAqB,CAAC,CAACc,SAAS,CAACH,cAAc,CAAC;MAC3Fb,YAAY,GAAGe,UAAU,CAAC/B,MAAM,CAAC,CAAC;;MAElC;MACA,MAAMF,oBAAoB,GAAG7E,2BAA2B,CAAC,IAAI0C,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC,EAAEhC,cAAc,CAAC8B,QAAQ,EAAE,qBAAqB,CAAC;MAClIoB,GAAG,GAAG7F,MAAM,CAAC0E,oBAAoB,EAAE,qBAAqB,CAAC,CAACE,MAAM,CAAC,CAAC;IACpE,CAAC,MAAM;MACL;MACAgB,YAAY,GAAG,IAAIrD,IAAI,CAACyC,IAAI,CAAC;MAC7BY,YAAY,CAACiB,QAAQ,CAACzB,KAAK,EAAEC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;MAC3CQ,GAAG,GAAG,IAAItD,IAAI,CAAC,CAAC;IAClB;IAEA,MAAMuE,MAAM,GAAGlB,YAAY,GAAGC,GAAG;IAEjC,OAAOiB,MAAM;EACf,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAACjC,GAAG,EAAEkC,QAAQ,KAAK;IAC1C,MAAMhC,IAAI,GAAG5F,OAAO,CAACgD,gBAAgB,EAAEW,UAAU,CAACkC,OAAO,CAACH,GAAG,CAAC,CAAC;IAE/DjD,eAAe,CAAC;MACdiD,GAAG;MACHkC,QAAQ;MACRhC,IAAI;MACJiC,aAAa,EAAE9H,MAAM,CAAC6F,IAAI,EAAE,YAAY,CAAC;MACzCkC,aAAa,EAAEF;IACjB,CAAC,CAAC;IAEFjF,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMoF,wBAAwB,GAAIC,QAAQ,IAAK;IAC7C,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEjD,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMC,WAAW,GAAG,CAAC,GAAGH,QAAQ,CAAC,CAACI,IAAI,CAAC,CAAC;IAExC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACF,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC3C,MAAMC,WAAW,GAAGH,WAAW,CAACE,CAAC,CAAC;MAClC,MAAM,CAACvC,SAAS,CAAC,GAAGwC,WAAW,CAACvC,KAAK,CAAC,GAAG,CAAC;MAC1C,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGH,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC;;MAEzD;MACA,IAAIF,OAAO,KAAK,CAAC,EAAE;QACjB;QACA,IAAIsC,QAAQ;QACZ,IAAIvC,KAAK,KAAK,EAAE,EAAE;UAChB;UACAuC,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM;UACLA,QAAQ,GAAG,GAAGvC,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAACP,KAAK,GAAG,CAAC,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;QACpG;QAEA,IAAI4B,WAAW,CAACK,QAAQ,CAACD,QAAQ,CAAC,EAAE;UAClC;UACAL,MAAM,CAACO,IAAI,CAAC;YACVC,IAAI,EAAE,MAAM;YACZC,SAAS,EAAEL,WAAW;YACtBM,OAAO,EAAEL,QAAQ;YACjBM,IAAI,EAAE7C;UACR,CAAC,CAAC;UACF;UACAqC,CAAC,EAAE;QACL,CAAC,MAAM;UACL;UACAH,MAAM,CAACO,IAAI,CAAC;YACVC,IAAI,EAAE,MAAM;YACZI,IAAI,EAAER,WAAW;YACjBO,IAAI,EAAE7C,KAAK;YACX+C,MAAM,EAAE9C;UACV,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA,MAAM+C,QAAQ,GAAG,GAAGhD,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,OAAOP,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;QAClG,IAAI,CAAC4B,WAAW,CAACK,QAAQ,CAACQ,QAAQ,CAAC,EAAE;UACnC;UACAd,MAAM,CAACO,IAAI,CAAC;YACVC,IAAI,EAAE,MAAM;YACZI,IAAI,EAAER,WAAW;YACjBO,IAAI,EAAE7C,KAAK;YACX+C,MAAM,EAAE9C;UACV,CAAC,CAAC;QACJ;MACF;IACF;IAEA,OAAOiC,MAAM;EACf,CAAC;;EAED;EACA,MAAMe,mBAAmB,GAAGA,CAACvD,GAAG,EAAEC,WAAW,EAAEuD,cAAc,KAAK;IAChE,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAACxD,GAAG,CAAC,EAAE,OAAO,KAAK;IAEzD,MAAMwC,MAAM,GAAGH,wBAAwB,CAACmB,cAAc,CAACxD,GAAG,CAAC,CAAC;IAC5D,MAAMyD,aAAa,GAAGjB,MAAM,CAACkB,IAAI,CAACC,KAAK,IACrCA,KAAK,CAACX,IAAI,KAAK,MAAM,KAAKW,KAAK,CAACV,SAAS,KAAKhD,WAAW,IAAI0D,KAAK,CAACT,OAAO,KAAKjD,WAAW,CAC5F,CAAC;IAED,IAAI,CAACwD,aAAa,EAAE,OAAO,KAAK;;IAEhC;IACA,MAAMG,aAAa,GAAG7D,YAAY,CAACC,GAAG,EAAEyD,aAAa,CAACR,SAAS,CAAC;IAChE,MAAMY,cAAc,GAAG9D,YAAY,CAACC,GAAG,EAAEyD,aAAa,CAACP,OAAO,CAAC;;IAE/D;IACA,OAAO,CAACU,aAAa,IAAI,CAACC,cAAc;EAC1C,CAAC;;EAED;EACA,MAAMC,0BAA0B,GAAGA,CAAC9D,GAAG,EAAEC,WAAW,EAAEuD,cAAc,KAAK;IACvE,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAACxD,GAAG,CAAC,EAAE,OAAO,KAAK;IAEzD,MAAM,CAACI,SAAS,CAAC,GAAGH,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC;IAC1C,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGH,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC;;IAEzD;IACA,IAAIF,OAAO,KAAK,EAAE,EAAE,OAAO,KAAK;;IAEhC;IACA,MAAMwD,iBAAiB,GAAG,GAAG,CAAC,CAACzD,KAAK,GAAG,CAAC,IAAI,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;;IAErI;IACA,IAAI,CAAC2C,cAAc,CAACxD,GAAG,CAAC,CAAC8C,QAAQ,CAACiB,iBAAiB,CAAC,EAAE,OAAO,KAAK;;IAElE;IACA,MAAMC,eAAe,GAAGjE,YAAY,CAACC,GAAG,EAAEC,WAAW,CAAC;IACtD,MAAMgE,YAAY,GAAGlE,YAAY,CAACC,GAAG,EAAE+D,iBAAiB,CAAC;IAEzD,OAAO,CAACC,eAAe,IAAI,CAACC,YAAY;EAC1C,CAAC;;EAED;EACA,MAAMC,2BAA2B,GAAGA,CAAClE,GAAG,EAAEC,WAAW,EAAEuD,cAAc,KAAK;IACxE,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAACxD,GAAG,CAAC,EAAE,OAAO,KAAK;IAEzD,MAAM,CAACI,SAAS,CAAC,GAAGH,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC;IAC1C,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGH,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC;;IAEzD;IACA,IAAIF,OAAO,KAAK,CAAC,EAAE;MACjB,MAAM4D,cAAc,GAAG,GAAG7D,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;;MAErH;MACA,IAAI2C,cAAc,CAACxD,GAAG,CAAC,CAAC8C,QAAQ,CAACqB,cAAc,CAAC,EAAE;QAChD,MAAMC,cAAc,GAAGrE,YAAY,CAACC,GAAG,EAAEmE,cAAc,CAAC;QACxD,IAAI,CAACC,cAAc,EAAE;UACnB;UACA,MAAML,iBAAiB,GAAG,GAAG,CAAC,CAACzD,KAAK,GAAG,CAAC,IAAI,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;UACrI,MAAMwD,WAAW,GAAGb,cAAc,CAACxD,GAAG,CAAC,CAAC8C,QAAQ,CAACiB,iBAAiB,CAAC,IAAI,CAAChE,YAAY,CAACC,GAAG,EAAE+D,iBAAiB,CAAC;UAE5G,OAAO;YACLO,iBAAiB,EAAE,IAAI;YACvBC,yBAAyB,EAAEF;UAC7B,CAAC;QACH;MACF;IACF;;IAEA;IACA,IAAI9D,OAAO,KAAK,EAAE,EAAE;MAClB,MAAMwD,iBAAiB,GAAG,GAAG,CAAC,CAACzD,KAAK,GAAG,CAAC,IAAI,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;MACrI,MAAMwD,WAAW,GAAGb,cAAc,CAACxD,GAAG,CAAC,CAAC8C,QAAQ,CAACiB,iBAAiB,CAAC,IAAI,CAAChE,YAAY,CAACC,GAAG,EAAE+D,iBAAiB,CAAC;MAE5G,OAAO;QACLO,iBAAiB,EAAE,IAAI;QACvBC,yBAAyB,EAAEF;MAC7B,CAAC;IACH;IAEA,OAAO;MACLC,iBAAiB,EAAE,KAAK;MACxBC,yBAAyB,EAAE;IAC7B,CAAC;EACH,CAAC;;EAID;EACA,MAAMC,eAAe,GAAGA,CAACxE,GAAG,EAAEC,WAAW,KAAK;IAC5C,IAAI,EAAC3D,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEmI,eAAe,GAAE,OAAO,KAAK;IAE3C,IAAI;MACF,MAAMjB,cAAc,GAAG,OAAOlH,OAAO,CAACmI,eAAe,KAAK,QAAQ,GAC9DC,IAAI,CAACC,KAAK,CAACrI,OAAO,CAACmI,eAAe,CAAC,GACnCnI,OAAO,CAACmI,eAAe;;MAE3B;MACA,MAAM,CAACrE,SAAS,CAAC,GAAGH,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC;MAC1C,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGH,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC;;MAEzD;MACA,MAAMmE,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,OAAO,KAAK;QACtD,MAAMC,KAAK,GAAGvB,cAAc,CAACqB,QAAQ,CAAC,IAAI,EAAE;QAC5C,OAAOE,KAAK,CAACjC,QAAQ,CAACgC,OAAO,CAAC,IAAI,CAAC/E,YAAY,CAAC8E,QAAQ,EAAEC,OAAO,CAAC;MACpE,CAAC;;MAED;MACA,IAAIvE,OAAO,KAAK,CAAC,EAAE;QACjB,MAAM4D,cAAc,GAAG,GAAG7D,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,CAACP,KAAK,GAAG,CAAC,IAAI,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;QACrH,OAAO+D,wBAAwB,CAAC5E,GAAG,EAAEmE,cAAc,CAAC;MACtD;;MAEA;MACA,IAAI5D,OAAO,KAAK,EAAE,EAAE;QAClB;QACA,MAAMyE,QAAQ,GAAG,CAAC1E,KAAK,GAAG,CAAC,IAAI,EAAE;QACjC,MAAM2E,WAAW,GAAGD,QAAQ,CAACpE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACxD,MAAMkD,iBAAiB,GAAG,GAAGkB,WAAW,OAAOA,WAAW,KAAK;QAE/D,MAAMC,eAAe,GAAGjH,UAAU,CAACkC,OAAO,CAACH,GAAG,CAAC;QAC/C;QACA,MAAMmF,WAAW,GAAG7E,KAAK,KAAK,EAAE,GAC5BrC,UAAU,CAAC,CAACiH,eAAe,GAAG,CAAC,IAAIjH,UAAU,CAACsE,MAAM,CAAC,GACrDvC,GAAG;QAEP,OAAO4E,wBAAwB,CAACO,WAAW,EAAEpB,iBAAiB,CAAC;MACjE;MAEA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOnH,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMwI,qBAAqB,GAAGA,CAACpF,GAAG,EAAEC,WAAW,KAAK;IAClD;IACA,IAAIF,YAAY,CAACC,GAAG,EAAEC,WAAW,CAAC,EAAE;MAClC,OAAO,CAAC;IACV;IAEA,MAAMC,IAAI,GAAG5F,OAAO,CAACgD,gBAAgB,EAAEW,UAAU,CAACkC,OAAO,CAACH,GAAG,CAAC,CAAC;IAE/DjD,eAAe,CAAC;MACdiD,GAAG;MACHkC,QAAQ,EAAEjC,WAAW;MACrBC,IAAI;MACJiC,aAAa,EAAE9H,MAAM,CAAC6F,IAAI,EAAE,YAAY;IAC1C,CAAC,CAAC;;IAEF;IACA;IACA;IACFtC,iBAAiB,CAAC,IAAI,CAAC;IAErBX,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMoI,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACvI,YAAY,IAAI,CAACf,WAAW,IAAI,CAACC,KAAK,EAAE;IAE7C,IAAI;MACF,MAAM,CAACoE,SAAS,CAAC,GAAGtD,YAAY,CAACoF,QAAQ,CAAC7B,KAAK,CAAC,GAAG,CAAC;;MAEpD;MACA,IAAIiF,eAAe;MACnB,IAAIzH,cAAc,IAAIA,cAAc,CAAC8B,QAAQ,EAAE;QAC7C;QACA;QACA,MAAM4F,eAAe,GAAGrK,MAAM,CAACsK,EAAE,CAC/B,GAAG1I,YAAY,CAACqF,aAAa,IAAI/B,SAAS,KAAK,EAC/C,qBAAqB,EACrBvC,cAAc,CAAC8B,QACjB,CAAC;;QAED;QACA2F,eAAe,GAAGC,eAAe,CAACE,KAAK,CAAC,CAAC,CAACC,QAAQ,CAACzK,mBAAmB,CAAC4C,cAAc,CAAC8B,QAAQ,CAAC,EAAE,SAAS,CAAC,CAACtF,MAAM,CAAC,qBAAqB,CAAC;MAC3I,CAAC,MAAM;QACL;QACAiL,eAAe,GAAG,GAAGxI,YAAY,CAACqF,aAAa,IAAI/B,SAAS,KAAK;MACnE;MAEAnB,OAAO,CAAC0G,GAAG,CAAC,0BAA0B,EAAE;QACtCC,QAAQ,EAAEjI,cAAc;QACxBgC,QAAQ,EAAE9B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8B,QAAQ;QAClCkG,gBAAgB,EAAE,GAAG/I,YAAY,CAACqF,aAAa,IAAI/B,SAAS,KAAK;QACjE0F,iBAAiB,EAAER;MACrB,CAAC,CAAC;MAEF,MAAM;QAAE1G;MAAK,CAAC,GAAG,MAAM5E,KAAK,CAAC+L,IAAI,CAAC,WAAW,EAAE;QAC7CC,UAAU,EAAEtK,EAAE;QACduK,QAAQ,EAAEX,eAAe;QACzBM,QAAQ,EAAEjI,cAAc;QACxBuI,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,IAAItH,IAAI,CAACG,OAAO,EAAE;QAEhB1B,cAAc,CAACuB,IAAI,CAACA,IAAI,CAAC;QAC3B;QACAzD,KAAK,CAAC4D,OAAO,CAACpD,CAAC,CAAC,+BAA+B,CAAC,EAAE;UAAEiK,QAAQ,EAAE;QAAK,CAAC,CAAC;QACnE3I,oBAAoB,CAAC,KAAK,CAAC;;QAE3B;QACA,IAAI;UACF,MAAMkJ,gBAAgB,GAAG,MAAMnM,KAAK,CAAC6E,GAAG,CAAC,mBAAmB,CAAC;UAC7D,IAAIsH,gBAAgB,CAACvH,IAAI,IAAIwH,KAAK,CAACC,OAAO,CAACF,gBAAgB,CAACvH,IAAI,CAAC0H,QAAQ,CAAC,EAAE;YAC1E;YACA,MAAMC,cAAc,GAAGJ,gBAAgB,CAACvH,IAAI,CAAC0H,QAAQ,CAAC5C,IAAI,CACxD8C,OAAO,IAAI,IAAI/I,IAAI,CAAC+I,OAAO,CAACC,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,KAAK,IAAIjJ,IAAI,CAAC6H,eAAe,CAAC,CAACoB,OAAO,CAAC,CAC5F,CAAC;YAED,IAAIH,cAAc,EAAE;cAClBtH,OAAO,CAAC0G,GAAG,CAAC,wBAAwB,EAAEY,cAAc,CAAC;cACrDlJ,cAAc,CAACsJ,QAAQ,KAAK;gBAC1B,GAAGA,QAAQ;gBACXH,OAAO,EAAED;cACX,CAAC,CAAC,CAAC;YACL;UACF;QACF,CAAC,CAAC,OAAOK,YAAY,EAAE;UACrB3H,OAAO,CAACrC,KAAK,CAAC,iCAAiC,EAAEgK,YAAY,CAAC;QAChE;MACF,CAAC,MAAM;QACLzJ,eAAe,CAACyB,IAAI,CAACU,OAAO,IAAI3D,CAAC,CAAC,uBAAuB,CAAC,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MAAA,IAAAiK,eAAA,EAAAC,oBAAA;MACd7H,OAAO,CAACrC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,EAAAiK,eAAA,GAAAjK,KAAK,CAACmK,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBjI,IAAI,cAAAkI,oBAAA,uBAApBA,oBAAA,CAAsBxH,OAAO,MAAK,wDAAwD,EAAE;QAC9FnC,eAAe,CAACxB,CAAC,CAAC,6BAA6B,CAAC,CAAC;MACnD,CAAC,MAAM;QAAA,IAAAqL,gBAAA,EAAAC,qBAAA;QACL9J,eAAe,CAAC,EAAA6J,gBAAA,GAAApK,KAAK,CAACmK,QAAQ,cAAAC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpI,IAAI,cAAAqI,qBAAA,uBAApBA,qBAAA,CAAsB3H,OAAO,KAAI3D,CAAC,CAAC,uBAAuB,CAAC,CAAC;MAC9E;IACF;EACF,CAAC;;EAED;EACA,MAAMuL,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAC5K,OAAO,IAAI,CAACA,OAAO,CAACmI,eAAe,EAAE,OAAO,IAAI;IAErD,oBACEpJ,OAAA,CAAC5D,IAAI;MAAC0P,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAC,QAAA,EACxBpJ,UAAU,CAACuC,GAAG,CAAC,CAACR,GAAG,EAAEsH,KAAK,KAAK;QAC9B,MAAMpH,IAAI,GAAG5F,OAAO,CAACgD,gBAAgB,EAAEgK,KAAK,CAAC;QAC7C,MAAMC,QAAQ,GAAGjL,OAAO,CAACmI,eAAe,CAACzE,GAAG,CAAC,IAAI,EAAE;QAEnD,oBACE3E,OAAA,CAAC5D,IAAI;UAAC+P,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eACrChM,OAAA,CAACvD,IAAI;YAAC+P,SAAS,EAAE,CAAE;YAACC,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAAV,QAAA,eACzChM,OAAA,CAACtD,WAAW;cAAAsP,QAAA,gBACVhM,OAAA,CAAC7D,GAAG;gBAACsQ,EAAE,EAAE;kBACPE,SAAS,EAAE,QAAQ;kBACnBC,EAAE,EAAE,CAAC;kBACLC,YAAY,EAAE,CAAC;kBACfC,WAAW,EAAE,SAAS;kBACtBC,OAAO,EAAEvM,KAAK,CAACwM,OAAO,CAACC,OAAO,CAACC,IAAI;kBACnCC,KAAK,EAAE,OAAO;kBACdC,EAAE,EAAE,CAAC;kBACLC,YAAY,EAAE,aAAa;kBAC3BC,EAAE,EAAE;gBACN,CAAE;gBAAAtB,QAAA,gBACAhM,OAAA,CAAC9D,UAAU;kBAACqR,OAAO,EAAC,IAAI;kBAAAvB,QAAA,EACrB1L,CAAC,CAAC,QAAQqE,GAAG,EAAE;gBAAC;kBAAA6I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACb3N,OAAA,CAAC9D,UAAU;kBAACqR,OAAO,EAAC,OAAO;kBAAAvB,QAAA,EACxBhN,MAAM,CAAC6F,IAAI,EAAE,aAAa,EAAE;oBAAE+I,MAAM,EAAE7M,KAAK,GAAGzB,EAAE,GAAGC;kBAAK,CAAC;gBAAC;kBAAAiO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EAELzB,QAAQ,CAAChF,MAAM,GAAG,CAAC,gBAClBlH,OAAA,CAAC7D,GAAG;gBAACsQ,EAAE,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEC,aAAa,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAA/B,QAAA,EAC3DE,QAAQ,CAAC/G,GAAG,CAAC,CAAC0B,QAAQ,EAAEmH,GAAG,kBAC1BhO,OAAA,CAAC3D,MAAM;kBAELkR,OAAO,EAAC,UAAU;kBAClBJ,KAAK,EAAC,SAAS;kBACfc,OAAO,EAAEA,CAAA,KAAMrH,gBAAgB,CAACjC,GAAG,EAAEkC,QAAQ,CAAE;kBAC/CqH,SAAS,eAAElO,OAAA,CAAChC,cAAc;oBAAAwP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC9BlB,EAAE,EAAE;oBAAE0B,cAAc,EAAE;kBAAa,CAAE;kBAAAnC,QAAA,EAEpCnF;gBAAQ,GAPJmH,GAAG;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQF,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN3N,OAAA,CAAC7D,GAAG;gBAACsQ,EAAE,EAAE;kBAAEE,SAAS,EAAE,QAAQ;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAApB,QAAA,eACtChM,OAAA,CAAC9D,UAAU;kBAACqR,OAAO,EAAC,OAAO;kBAACJ,KAAK,EAAC,gBAAgB;kBAAAnB,QAAA,EAC/C1L,CAAC,CAAC,0BAA0B;gBAAC;kBAAAkN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA7CoChJ,GAAG;UAAA6I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8C1C,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;;EAED;EACA,MAAMS,oBAAoB,GAAGA,CAAA,kBAC3BpO,OAAA,CAAC/D,KAAK;IAACuQ,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAE4B,CAAC,EAAE,CAAC;MAAE1B,SAAS,EAAE,QAAQ;MAAE2B,EAAE,EAAE;IAAE,CAAE;IAAAtC,QAAA,gBAC5DhM,OAAA,CAAC9B,eAAe;MAACiP,KAAK,EAAC,SAAS;MAACV,EAAE,EAAE;QAAE8B,QAAQ,EAAE,EAAE;QAAEjB,EAAE,EAAE;MAAE;IAAE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChE3N,OAAA,CAAC9D,UAAU;MAACqR,OAAO,EAAC,IAAI;MAACiB,YAAY;MAAAxC,QAAA,EAClC1L,CAAC,CAAC,6BAA6B;IAAC;MAAAkN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,eACb3N,OAAA,CAAC9D,UAAU;MAACqR,OAAO,EAAC,OAAO;MAACkB,SAAS;MAAAzC,QAAA,EAClC1L,CAAC,CAAC,+BAA+B;IAAC;MAAAkN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,EAEZ,CAAA5L,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoJ,OAAO,kBACnBnL,OAAA,CAAC7D,GAAG;MAACsQ,EAAE,EAAE;QAAEiC,EAAE,EAAE,CAAC;QAAEpB,EAAE,EAAE,CAAC;QAAEe,CAAC,EAAE,CAAC;QAAEtB,OAAO,EAAE,kBAAkB;QAAEM,YAAY,EAAE,CAAC;QAAEsB,SAAS,EAAE;MAAE,CAAE;MAAA3C,QAAA,gBAC1FhM,OAAA,CAAC9D,UAAU;QAACqR,OAAO,EAAC,IAAI;QAACiB,YAAY;QAACrB,KAAK,EAAC,SAAS;QAAAnB,QAAA,EAClD1L,CAAC,CAAC,wBAAwB;MAAC;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACb3N,OAAA,CAAC9D,UAAU;QAACqR,OAAO,EAAC,OAAO;QAACiB,YAAY;QAAAxC,QAAA,gBACtChM,OAAA;UAAAgM,QAAA,GAAS1L,CAAC,CAAC,eAAe,CAAC,EAAC,GAAC;QAAA;UAAAkN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC5L,WAAW,CAACoJ,OAAO,CAACyD,YAAY;MAAA;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACb3N,OAAA,CAAC9D,UAAU;QAACqR,OAAO,EAAC,OAAO;QAACiB,YAAY;QAAAxC,QAAA,gBACtChM,OAAA;UAAAgM,QAAA,GAAS1L,CAAC,CAAC,eAAe,CAAC,EAAC,GAAC;QAAA;UAAAkN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC3O,MAAM,CAAC,IAAIoD,IAAI,CAACL,WAAW,CAACoJ,OAAO,CAACC,YAAY,CAAC,EAAE,MAAM,EAAE;UAAEwC,MAAM,EAAE7M,KAAK,GAAGzB,EAAE,GAAGC;QAAK,CAAC,CAAC;MAAA;QAAAiO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvH,CAAC,eACb3N,OAAA,CAAC9D,UAAU;QAACqR,OAAO,EAAC,OAAO;QAACiB,YAAY;QAAAxC,QAAA,gBACtChM,OAAA;UAAAgM,QAAA,GAAS1L,CAAC,CAAC,mBAAmB,CAAC,EAAC,GAAC;QAAA;UAAAkN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC5L,WAAW,CAACoJ,OAAO,CAACZ,QAAQ,EAAC,GAAC,EAACjK,CAAC,CAAC,kBAAkB,CAAC;MAAA;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC,eACb3N,OAAA,CAAC9D,UAAU;QAACqR,OAAO,EAAC,OAAO;QAACJ,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,EAC9D1L,CAAC,CAAC,2BAA2B;MAAC;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAED3N,OAAA,CAAC7D,GAAG;MAACsQ,EAAE,EAAE;QAAEiC,EAAE,EAAE,CAAC;QAAEb,OAAO,EAAE,MAAM;QAAEM,cAAc,EAAE,QAAQ;QAAEJ,GAAG,EAAE;MAAE,CAAE;MAAA/B,QAAA,gBACpEhM,OAAA,CAAC3D,MAAM;QACLkR,OAAO,EAAC,UAAU;QAClBsB,SAAS,EAAE/S,IAAK;QAChBgT,EAAE,EAAE,oBAAoBzO,EAAE,EAAG;QAC7B6N,SAAS,eAAElO,OAAA,CAACpC,aAAa;UAAA4P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA3B,QAAA,EAE5B1L,CAAC,CAAC,uBAAuB;MAAC;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACT3N,OAAA,CAAC3D,MAAM;QACLkR,OAAO,EAAC,WAAW;QACnBsB,SAAS,EAAE/S,IAAK;QAChBgT,EAAE,EAAC,mBAAmB;QACtB3B,KAAK,EAAC,SAAS;QAAAnB,QAAA,EAEd1L,CAAC,CAAC,wBAAwB;MAAC;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACT3N,OAAA,CAAC3D,MAAM;QACLkR,OAAO,EAAC,UAAU;QAClBsB,SAAS,EAAE/S,IAAK;QAChBgT,EAAE,EAAC,mBAAmB;QACtB3B,KAAK,EAAC,WAAW;QAAAnB,QAAA,EAEhB1L,CAAC,CAAC,wBAAwB;MAAC;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CACR;;EAED;EACA,MAAMoB,mBAAmB,GAAGA,CAAA;IAAA,IAAAC,KAAA,EAAAC,KAAA,EAAAC,sBAAA;IAAA,oBAC1BlP,OAAA,CAAClD,MAAM;MAACqS,IAAI,EAAExN,iBAAkB;MAACyN,OAAO,EAAEA,CAAA,KAAMxN,oBAAoB,CAAC,KAAK,CAAE;MAAAoK,QAAA,gBAC1EhM,OAAA,CAACjD,WAAW;QAAAiP,QAAA,GACT1L,CAAC,CAAC,wBAAwB,CAAC,eAC5BN,OAAA,CAAC5C,UAAU;UACT,cAAW,OAAO;UAClB6Q,OAAO,EAAEA,CAAA,KAAMrM,oBAAoB,CAAC,KAAK,CAAE;UAC3C6K,EAAE,EAAE;YAAE4C,QAAQ,EAAE,UAAU;YAAEC,KAAK,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAvD,QAAA,eAE/ChM,OAAA,CAAC1B,SAAS;YAAAkP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACd3N,OAAA,CAAChD,aAAa;QAAAgP,QAAA,EACXvK,YAAY,iBACXzB,OAAA,CAAC7D,GAAG;UAACsQ,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,gBACjBhM,OAAA,CAAC9D,UAAU;YAACqR,OAAO,EAAC,OAAO;YAACiB,YAAY;YAAAxC,QAAA,gBACtChM,OAAA;cAAAgM,QAAA,GAAS1L,CAAC,CAAC,iBAAiB,CAAC,EAAC,GAAC;YAAA;cAAAkN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC1M,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuO,SAAS;UAAA;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACb3N,OAAA,CAAC9D,UAAU;YAACqR,OAAO,EAAC,OAAO;YAACiB,YAAY;YAAAxC,QAAA,gBACtChM,OAAA;cAAAgM,QAAA,GAAS1L,CAAC,CAAC,aAAa,CAAC,EAAC,GAAC;YAAA;cAAAkN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrN,CAAC,CAAC,QAAQmB,YAAY,CAACkD,GAAG,EAAE,CAAC;UAAA;YAAA6I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACb3N,OAAA,CAAC9D,UAAU;YAACqR,OAAO,EAAC,OAAO;YAACiB,YAAY;YAAAxC,QAAA,gBACtChM,OAAA;cAAAgM,QAAA,GAAS1L,CAAC,CAAC,cAAc,CAAC,EAAC,GAAC;YAAA;cAAAkN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3O,MAAM,CAACyC,YAAY,CAACoD,IAAI,EAAE,KAAK,EAAE;cAAE+I,MAAM,EAAE7M,KAAK,GAAGzB,EAAE,GAAGC;YAAK,CAAC,CAAC;UAAA;YAAAiO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eACb3N,OAAA,CAAC9D,UAAU;YAACqR,OAAO,EAAC,OAAO;YAACiB,YAAY;YAAAxC,QAAA,gBACtChM,OAAA;cAAAgM,QAAA,GAAS1L,CAAC,CAAC,cAAc,CAAC,EAAC,GAAC;YAAA;cAAAkN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAAC,MAAM;cAC5C,IAAIrL,cAAc,KAAK,IAAI,IAAIb,YAAY,EAAE;gBAC3C;gBACA,MAAM,CAACsD,SAAS,CAAC,GAAGtD,YAAY,CAACoF,QAAQ,CAAC7B,KAAK,CAAC,GAAG,CAAC;gBACpD,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGH,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,CAACC,MAAM,CAAC;;gBAEzD;gBACA,MAAMqK,YAAY,GAAGxK,KAAK,GAAG,EAAE,GAAGC,OAAO;gBACzC,MAAMwK,UAAU,GAAGD,YAAY,GAAG,EAAE,CAAC,CAAC;gBACtC,MAAME,QAAQ,GAAG3J,IAAI,CAACG,KAAK,CAACuJ,UAAU,GAAG,EAAE,CAAC;gBAC5C,MAAME,OAAO,GAAGF,UAAU,GAAG,EAAE;gBAE/B,MAAMG,aAAa,GAAG,GAAGtJ,MAAM,CAACoJ,QAAQ,CAAC,CAACnK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIe,MAAM,CAACqJ,OAAO,CAAC,CAACpK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;gBAEhG,OAAO,GAAGT,SAAS,MAAM8K,aAAa,KAAKvP,CAAC,CAAC,oBAAoB,CAAC,GAAG;cACvE,CAAC,MAAM;gBACL;gBACA,OAAOmB,YAAY,CAACoF,QAAQ;cAC9B;YACF,CAAC,EAAE,CAAC;UAAA;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACb3N,OAAA,CAAC9D,UAAU;YAACqR,OAAO,EAAC,OAAO;YAACiB,YAAY;YAAAxC,QAAA,gBACtChM,OAAA;cAAAgM,QAAA,GAAS1L,CAAC,CAAC,kBAAkB,CAAC,EAAC,GAAC;YAAA;cAAAkN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrL,cAAc,EAAC,GAAC,EAAChC,CAAC,CAAC,kBAAkB,CAAC,EAChFgC,cAAc,KAAK,IAAI,GAAG,KAAKhC,CAAC,CAAC,oBAAoB,CAAC,GAAG,GAAG,KAAKA,CAAC,CAAC,oBAAoB,CAAC,GAAG;UAAA;YAAAkN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACb3N,OAAA,CAAC9D,UAAU;YAACqR,OAAO,EAAC,OAAO;YAACiB,YAAY;YAAAxC,QAAA,gBACtChM,OAAA;cAAAgM,QAAA,GAAS1L,CAAC,CAAC,eAAe,CAAC,EAAC,GAAC;YAAA;cAAAkN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,MAAE,EAAC,CAAC,MAAM;cAC9C,IAAIrL,cAAc,KAAK,IAAI,EAAE;gBAC3B,OAAOrB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6O,gBAAgB;cAClC,CAAC,MAAM;gBACL,IAAI3O,eAAe,EAAE;kBAAA,IAAA4O,IAAA,EAAAC,KAAA,EAAAC,qBAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,sBAAA,EAAAC,KAAA,EAAAC,sBAAA;kBAC3B,OAAO,EAAAP,IAAA,IAAAC,KAAA,IAAAC,qBAAA,GAAChP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsP,gBAAgB,cAAAN,qBAAA,cAAAA,qBAAA,GAAIhP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuP,kBAAkB,cAAAR,KAAA,cAAAA,KAAA,GAAI/O,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwP,gBAAgB,cAAAV,IAAA,cAAAA,IAAA,GAAK,CAAA9O,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6O,gBAAgB,IAAG,CAAC,EAAGY,OAAO,GAAG,EAAAR,KAAA,IAAAC,KAAA,IAAAC,sBAAA,GAACnP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsP,gBAAgB,cAAAH,sBAAA,cAAAA,sBAAA,GAAInP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuP,kBAAkB,cAAAL,KAAA,cAAAA,KAAA,GAAIlP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwP,gBAAgB,cAAAP,KAAA,cAAAA,KAAA,GAAK,CAAAjP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6O,gBAAgB,IAAG,CAAC,EAAGY,OAAO,CAAC,CAAC,CAAC,IAAAL,KAAA,IAAAC,sBAAA,GAAIrP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsP,gBAAgB,cAAAD,sBAAA,cAAAA,sBAAA,GAAIrP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuP,kBAAkB,cAAAH,KAAA,cAAAA,KAAA,GAAIpP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwP,gBAAiB;gBAC7W;gBACA,OAAO,CAAC,CAAAxP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6O,gBAAgB,IAAG,CAAC,EAAEY,OAAO,CAAC,CAAC,CAAC;cAC3C;YACF,CAAC,EAAE,CAAC,EAAC,GAAC,EAACpQ,CAAC,CAAC,iBAAiB,CAAC;UAAA;YAAAkN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eAEb3N,OAAA,CAACvC,WAAW;YAACoR,SAAS,EAAC,UAAU;YAACpC,EAAE,EAAE;cAAEiC,EAAE,EAAE,CAAC;cAAEpB,EAAE,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBACrDhM,OAAA,CAACtC,SAAS;cAACmR,SAAS,EAAC,QAAQ;cAAA7C,QAAA,EAAE1L,CAAC,CAAC,wBAAwB;YAAC;cAAAkN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvE3N,OAAA,CAAC1C,UAAU;cACTqT,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAEtO,cAAe;cACtBuO,QAAQ,EAAG7M,CAAC,IAAK;gBACfzB,iBAAiB,CAACyB,CAAC,CAAC8M,MAAM,CAACF,KAAK,CAAC;cACnC,CAAE;cAAA5E,QAAA,gBAEFhM,OAAA,CAACzC,gBAAgB;gBACfqT,KAAK,EAAC,IAAI;gBACVG,OAAO,eAAE/Q,OAAA,CAACxC,KAAK;kBAAAgQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBqD,KAAK,EAAE,GAAG1Q,CAAC,CAAC,oBAAoB,CAAC,QAAQA,CAAC,CAAC,kBAAkB,CAAC;cAAI;gBAAAkN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,EACDlM,YAAY,IAAI0H,eAAe,CAAC1H,YAAY,CAACkD,GAAG,EAAElD,YAAY,CAACoF,QAAQ,CAAC,iBACvE7G,OAAA,CAACzC,gBAAgB;gBACfqT,KAAK,EAAC,IAAI;gBACVG,OAAO,eAAE/Q,OAAA,CAACxC,KAAK;kBAAAgQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBqD,KAAK,EAAE,GAAG1Q,CAAC,CAAC,oBAAoB,CAAC,QAAQA,CAAC,CAAC,kBAAkB,CAAC,OAAOA,CAAC,CAAC,0BAA0B,CAAC;cAAG;gBAAAkN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAEbxM,eAAe,IAAImB,cAAc,KAAK,IAAI,iBACzCtC,OAAA,CAACzD,KAAK;YAAC0U,QAAQ,EAAC,MAAM;YAACxE,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,EAClC1L,CAAC,CAAC,6BAA6B,EAAE;cAAE4Q,KAAK,GAAAlC,KAAA,IAAAC,KAAA,IAAAC,sBAAA,GAAEjO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsP,gBAAgB,cAAArB,sBAAA,cAAAA,sBAAA,GAAIjO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuP,kBAAkB,cAAAvB,KAAA,cAAAA,KAAA,GAAIhO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwP,gBAAgB,cAAAzB,KAAA,cAAAA,KAAA,GAAI;YAAG,CAAC;UAAC;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpI,CACR,EAEF9L,YAAY,iBACT7B,OAAA,CAACzD,KAAK;YAAC0U,QAAQ,EAAC,OAAO;YAACxE,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,EACnCnK;UAAY;YAAA2L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB3N,OAAA,CAAC/C,aAAa;QAAA+O,QAAA,gBACZhM,OAAA,CAAC3D,MAAM;UAAC4R,OAAO,EAAEA,CAAA,KAAMrM,oBAAoB,CAAC,KAAK,CAAE;UAAAoK,QAAA,EAChD1L,CAAC,CAAC,eAAe;QAAC;UAAAkN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACT3N,OAAA,CAAC3D,MAAM;UACL4R,OAAO,EAAEjE,oBAAqB;UAC9BuD,OAAO,EAAC,WAAW;UACnBJ,KAAK,EAAC,SAAS;UACfgE,SAAS;UAAAnF,QAAA,EAER1L,CAAC,CAAC,uBAAuB;QAAC;UAAAkN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,CACV;;EAED;EACA,MAAMyD,OAAO,gBACXpR,OAAA,CAAChE,SAAS;IAACqV,QAAQ,EAAC,IAAI;IAAC5E,EAAE,EAAE;MAAEW,EAAE,EAAE;IAAE,CAAE;IAAApB,QAAA,EACpC3K,OAAO,gBACNrB,OAAA,CAAC7D,GAAG;MAACsQ,EAAE,EAAE;QAAEoB,OAAO,EAAE,MAAM;QAAEM,cAAc,EAAE,QAAQ;QAAEmD,UAAU,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAvF,QAAA,eAC9FhM,OAAA,CAAC1D,gBAAgB;QAAAkR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,GACJpM,KAAK,gBACPvB,OAAA,CAAC7D,GAAG;MAAA6P,QAAA,gBACFhM,OAAA,CAACzD,KAAK;QAAC0U,QAAQ,EAAC,OAAO;QAAAjF,QAAA,EAAEzK;MAAK;QAAAiM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACvC3N,OAAA,CAAC7D,GAAG;QAACsQ,EAAE,EAAE;UAAEiC,EAAE,EAAE,CAAC;UAAEb,OAAO,EAAE,MAAM;UAAEM,cAAc,EAAE;QAAS,CAAE;QAAAnC,QAAA,eAC5DhM,OAAA,CAAC3D,MAAM;UACLkR,OAAO,EAAC,WAAW;UACnBW,SAAS,eAAElO,OAAA,CAACpC,aAAa;YAAA4P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BkB,SAAS,EAAE/S,IAAK;UAChBgT,EAAE,EAAC,uBAAuB;UAAA9C,QAAA,EAEzB1L,CAAC,CAAC,6BAA6B;QAAC;UAAAkN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAEN3N,OAAA,CAACjB,sBAAsB;MAACyS,WAAW,EAAE,CAAC,2BAA2B,EAAE,oBAAoB,CAAE;MAAAxF,QAAA,eACzFhM,OAAA,CAAAE,SAAA;QAAA8L,QAAA,gBAEEhM,OAAA,CAAC7D,GAAG;UAACsQ,EAAE,EAAE;YAAEa,EAAE,EAAE,CAAC;YAAEO,OAAO,EAAE,MAAM;YAAEM,cAAc,EAAE;UAAa,CAAE;UAAAnC,QAAA,eAChEhM,OAAA,CAAC3D,MAAM;YACLkR,OAAO,EAAC,UAAU;YAClBW,SAAS,eAAElO,OAAA,CAACpC,aAAa;cAAA4P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BkB,SAAS,EAAE/S,IAAK;YAChBgT,EAAE,EAAE,oBAAoBzO,EAAE,EAAG;YAAA2L,QAAA,EAE5B1L,CAAC,CAAC,uBAAuB;UAAC;YAAAkN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN3N,OAAA,CAAC/D,KAAK;UAACuQ,SAAS,EAAE,CAAE;UAACC,EAAE,EAAE;YAAE4B,CAAC,EAAE,CAAC;YAAEf,EAAE,EAAE,CAAC;YAAED,YAAY,EAAE,CAAC;YAAEN,OAAO,EAAE,cAAc;YAAEI,KAAK,EAAE;UAAQ,CAAE;UAAAnB,QAAA,eACjGhM,OAAA,CAAC7D,GAAG;YAACsQ,EAAE,EAAE;cAAEoB,OAAO,EAAE,MAAM;cAAEM,cAAc,EAAE,eAAe;cAAEmD,UAAU,EAAE,QAAQ;cAAEG,QAAQ,EAAE,MAAM;cAAE1D,GAAG,EAAE;YAAE,CAAE;YAAA/B,QAAA,gBAC5GhM,OAAA,CAAC7D,GAAG;cAAA6P,QAAA,gBACFhM,OAAA,CAAC9D,UAAU;gBAACqR,OAAO,EAAC,IAAI;gBAACsB,SAAS,EAAC,IAAI;gBAACpC,EAAE,EAAE;kBAAEiF,UAAU,EAAE;gBAAO,CAAE;gBAAA1F,QAAA,GAChE1L,CAAC,CAAC,wBAAwB,CAAC,EAAC,GAAC,EAACW,OAAO,CAACuO,SAAS;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACb3N,OAAA,CAAC9D,UAAU;gBAACqR,OAAO,EAAC,OAAO;gBAACd,EAAE,EAAE;kBAAEkF,OAAO,EAAE;gBAAI,CAAE;gBAAA3F,QAAA,GAC9C1L,CAAC,CAAC,wBAAwB,CAAC,EAAC,KAAG,EAACW,OAAO,CAAC6O,gBAAgB,EAAC,GAAC,EAACxP,CAAC,CAAC,iBAAiB,CAAC;cAAA;gBAAAkN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3N,OAAA,CAAC7D,GAAG;cAACsQ,EAAE,EAAE;gBAAEE,SAAS,EAAE;cAAQ,CAAE;cAAAX,QAAA,gBAC9BhM,OAAA,CAAC9D,UAAU;gBAACqR,OAAO,EAAC,IAAI;gBAACd,EAAE,EAAE;kBAAEiF,UAAU,EAAE;gBAAO,CAAE;gBAAA1F,QAAA,EACjDxJ,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAE8B,QAAQ,GACvBzE,MAAM,CAACH,2BAA2B,CAAC,IAAI0C,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC,EAAEhC,cAAc,CAAC8B,QAAQ,EAAE,qBAAqB,CAAC,EAAE,qBAAqB,CAAC,CAACtF,MAAM,CAAC,gCAAgC,CAAC,GAE7KA,MAAM,CAAC0D,WAAW,EAAE,MAAM,EAAE;kBAC1BkL,MAAM,EAAE7M,KAAK,GAAGzB,EAAE,GAAGC;gBACvB,CAAC;cACF;gBAAAiO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,EACZ,CAAAnL,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8B,QAAQ,kBACvBtE,OAAA,CAAC9D,UAAU;gBAACqR,OAAO,EAAC,SAAS;gBAACd,EAAE,EAAE;kBAAEkF,OAAO,EAAE;gBAAI,CAAE;gBAAA3F,QAAA,EAChDxJ,cAAc,CAAC8B;cAAQ;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR3N,OAAA,CAAC/D,KAAK;UAACuQ,SAAS,EAAE,CAAE;UAACC,EAAE,EAAE;YAAE4B,CAAC,EAAE,CAAC;YAAEf,EAAE,EAAE,CAAC;YAAED,YAAY,EAAE;UAAE,CAAE;UAAArB,QAAA,gBACxDhM,OAAA,CAAC7D,GAAG;YAACsQ,EAAE,EAAE;cAAEoB,OAAO,EAAE,MAAM;cAAEyD,UAAU,EAAE,YAAY;cAAEvD,GAAG,EAAE;YAAE,CAAE;YAAA/B,QAAA,gBAC7DhM,OAAA,CAAC5B,QAAQ;cAAC+O,KAAK,EAAC,SAAS;cAACV,EAAE,EAAE;gBAAEiC,EAAE,EAAE;cAAI;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C3N,OAAA,CAAC9D,UAAU;cAACqR,OAAO,EAAC,IAAI;cAACiB,YAAY;cAAAxC,QAAA,EAClC1L,CAAC,CAAC,sBAAsB;YAAC;cAAAkN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN3N,OAAA,CAAC9D,UAAU;YAACqR,OAAO,EAAC,OAAO;YAACkB,SAAS;YAAAzC,QAAA,EAClC1L,CAAC,CAAC,0BAA0B;UAAC;YAAAkN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGR3N,OAAA,CAAC/D,KAAK;UAACuQ,SAAS,EAAE,CAAE;UAACC,EAAE,EAAE;YAAE4B,CAAC,EAAE,CAAC;YAAEf,EAAE,EAAE,CAAC;YAAED,YAAY,EAAE;UAAE,CAAE;UAAArB,QAAA,eACxDhM,OAAA,CAAC7D,GAAG;YAACsQ,EAAE,EAAE;cACPoB,OAAO,EAAE,MAAM;cACfM,cAAc,EAAE,eAAe;cAC/BmD,UAAU,EAAE,QAAQ;cACpBG,QAAQ,EAAE,MAAM;cAChB1D,GAAG,EAAE,CAAC;cACN6D,UAAU,EAAE,2BAA2BpR,KAAK,CAACwM,OAAO,CAACC,OAAO,CAACC,IAAI,QAAQ1M,KAAK,CAACwM,OAAO,CAACC,OAAO,CAAC4E,IAAI,QAAQ;cAC3G1E,KAAK,EAAE,OAAO;cACdkB,CAAC,EAAE,CAAC;cACJyD,CAAC,EAAE,CAAC,CAAC;cACLxE,EAAE,EAAE,CAAC;cACLD,YAAY,EAAE;YAChB,CAAE;YAAArB,QAAA,gBACAhM,OAAA,CAAC7D,GAAG;cAAA6P,QAAA,gBACFhM,OAAA,CAAC9D,UAAU;gBAACqR,OAAO,EAAC,IAAI;gBAACd,EAAE,EAAE;kBAAEiF,UAAU,EAAE,MAAM;kBAAEpE,EAAE,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,GAAC,eACvD,EAAC1L,CAAC,CAAC,wBAAwB,CAAC;cAAA;gBAAAkN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACb3N,OAAA,CAAC9D,UAAU;gBAACqR,OAAO,EAAC,IAAI;gBAACd,EAAE,EAAE;kBAAEiF,UAAU,EAAE;gBAAO,CAAE;gBAAA1F,QAAA,GACjD1L,CAAC,CAAC,gBAAgB,CAAC,EAAC,GAAC,EAACtB,MAAM,CAACiD,gBAAgB,EAAE,OAAO,EAAE;kBAAE2L,MAAM,EAAE7M,KAAK,GAAGzB,EAAE,GAAGC;gBAAK,CAAC,CAAC,EAAC,KAAG,EAACP,MAAM,CAACC,OAAO,CAACgD,gBAAgB,EAAE,CAAC,CAAC,EAAE,aAAa,EAAE;kBAAE2L,MAAM,EAAE7M,KAAK,GAAGzB,EAAE,GAAGC;gBAAK,CAAC,CAAC;cAAA;gBAAAiO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3N,OAAA,CAAC7D,GAAG;cAACsQ,EAAE,EAAE;gBAAEoB,OAAO,EAAE,MAAM;gBAAEE,GAAG,EAAE;cAAE,CAAE;cAAA/B,QAAA,gBACnChM,OAAA,CAAC3C,OAAO;gBAAC0U,KAAK,EAAEzR,CAAC,CAAC,sBAAsB,CAAE;gBAAA0L,QAAA,eACxChM,OAAA;kBAAAgM,QAAA,eACEhM,OAAA,CAAC5C,UAAU;oBACT6Q,OAAO,EAAEpL,gBAAiB;oBAC1BmP,QAAQ,EAAE5O,sBAAsB,CAAC,CAAE;oBACnCqJ,EAAE,EAAE;sBACFU,KAAK,EAAE,OAAO;sBACdJ,OAAO,EAAE,0BAA0B;sBACnC,SAAS,EAAE;wBACTA,OAAO,EAAE;sBACX,CAAC;sBACD,YAAY,EAAE;wBACZI,KAAK,EAAE,0BAA0B;wBACjCJ,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAAf,QAAA,eAEFhM,OAAA,CAACxB,eAAe;sBAAAgP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACV3N,OAAA,CAAC3C,OAAO;gBAAC0U,KAAK,EAAEzR,CAAC,CAAC,kBAAkB,CAAE;gBAAA0L,QAAA,eACpChM,OAAA;kBAAAgM,QAAA,eACEhM,OAAA,CAAC5C,UAAU;oBACT6Q,OAAO,EAAEjL,YAAa;oBACtBgP,QAAQ,EAAE3O,kBAAkB,CAAC,CAAE;oBAC/BoJ,EAAE,EAAE;sBACFU,KAAK,EAAE,OAAO;sBACdJ,OAAO,EAAE,0BAA0B;sBACnC,SAAS,EAAE;wBACTA,OAAO,EAAE;sBACX,CAAC;sBACD,YAAY,EAAE;wBACZI,KAAK,EAAE,0BAA0B;wBACjCJ,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAAf,QAAA,eAEFhM,OAAA,CAACtB,gBAAgB;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR3N,OAAA,CAAC/D,KAAK;UAACuQ,SAAS,EAAE,CAAE;UAACC,EAAE,EAAE;YAAE4B,CAAC,EAAE,CAAC;YAAEf,EAAE,EAAE,CAAC;YAAED,YAAY,EAAE;UAAE,CAAE;UAAArB,QAAA,gBACxDhM,OAAA,CAAC9D,UAAU;YAACqR,OAAO,EAAC,IAAI;YAACiB,YAAY;YAAC/B,EAAE,EAAE;cAAEoB,OAAO,EAAE,MAAM;cAAEyD,UAAU,EAAE,QAAQ;cAAEvD,GAAG,EAAE,CAAC;cAAET,EAAE,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBACjGhM,OAAA,CAAClC,iBAAiB;cAACqP,KAAK,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACpCrN,CAAC,CAAC,wBAAwB,CAAC;UAAA;YAAAkN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,EAEZ1M,OAAO,CAACmI,eAAe,IAAI,CAAC,MAAM;YACjC,IAAI;cACF,MAAM6I,WAAW,GAAG,OAAOhR,OAAO,CAACmI,eAAe,KAAK,QAAQ,GAC3DC,IAAI,CAACC,KAAK,CAACrI,OAAO,CAACmI,eAAe,CAAC,GACnCnI,OAAO,CAACmI,eAAe;cAC3B,oBACEpJ,OAAA,CAAClB,kBAAkB;gBACjBqJ,cAAc,EAAE8J,WAAY;gBAC5B5Q,OAAO,EAAEA,OAAQ;gBACjB6Q,SAAS,EAAE,IAAK;gBAChBC,YAAY,EAAEpI,qBAAsB;gBACpC9H,gBAAgB,EAAEA,gBAAiB;gBACnCW,UAAU,EAAEA,UAAW;gBACvB8B,YAAY,EAAEA,YAAa;gBAC3B0N,eAAe,EAAE5P,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8B;cAAS;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAEN,CAAC,CAAC,OAAOpM,KAAK,EAAE;cACdqC,OAAO,CAACrC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;cACtD,OAAOsK,oBAAoB,CAAC,CAAC;YAC/B;UACF,CAAC,EAAE,CAAC;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGPoB,mBAAmB,CAAC,CAAC;MAAA,eACtB;IAAC;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACqB;EACzB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CACZ;EAED,oBAAO3N,OAAA,CAACnB,MAAM;IAAAmN,QAAA,EAAEoF;EAAO;IAAA5D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAS,CAAC;AACnC,CAAC;AAACvN,EAAA,CA/6BID,WAAW;EAAA,QACAvE,SAAS,EACJG,cAAc,EACpBmB,QAAQ,EACLrB,WAAW,EACG+C,OAAO,EACrBzB,aAAa;AAAA;AAAAkV,EAAA,GAN1BlS,WAAW;AAi7BjB,eAAeA,WAAW;AAAC,IAAAkS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}