const mysql = require('mysql2/promise');
const config = require('../config/db.config');

/**
 * Get public teacher profile by ID
 */
const getTeacherById = async (req, res) => {
  let connection;
  try {
    const { id } = req.params;

    connection = await mysql.createConnection(config);

    // Create table if it doesn't exist
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS teacher_weekly_breaks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_profile_id INT NOT NULL,
        datetime DATETIME NOT NULL COMMENT 'Break date and time in UTC',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (teacher_profile_id) REFERENCES teacher_profiles(id) ON DELETE CASCADE,
        INDEX idx_teacher_profile_datetime (teacher_profile_id, datetime),
        INDEX idx_datetime (datetime),
        UNIQUE KEY unique_teacher_break (teacher_profile_id, datetime)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Get teacher profile with user data
    const [teachers] = await connection.execute(
      `SELECT
        u.id,
        u.full_name,
        u.email,
        u.gender,
        u.profile_picture_url as user_profile_picture,
        u.created_at as user_created_at,
        tp.id as profile_id,
        tp.phone,
        tp.country,
        tp.residence,
        tp.native_language,
        tp.teaching_languages,
        tp.course_types,
        tp.qualifications,
        tp.teaching_experience,
        tp.intro_video_url,
        tp.cv,
        tp.profile_picture_url,
        tp.available_hours,
        tp.price_per_lesson,
        tp.trial_lesson_price,
        tp.timezone,
        tp.payment_method,
        tp.status,
        tp.created_at as profile_created_at,
        tp.updated_at as profile_updated_at,
        COALESCE(AVG(r.rating), 0) as average_rating,
        COUNT(DISTINCT r.id) as review_count
      FROM users u
      JOIN teacher_profiles tp ON u.id = tp.user_id
      LEFT JOIN reviews r ON tp.id = r.teacher_profile_id
      WHERE u.id = ? AND u.role = 'platform_teacher' AND tp.status = 'approved'
      GROUP BY u.id`,
      [id]
    );

    if (teachers.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const teacher = teachers[0];

    // Get current week's breaks for this teacher
    const today = new Date();
    const currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay() + 1); // Monday
    const currentWeekEnd = new Date(currentWeekStart);
    currentWeekEnd.setDate(currentWeekStart.getDate() + 6); // Sunday

    const [weeklyBreaks] = await connection.execute(
      `SELECT datetime FROM teacher_weekly_breaks
       WHERE teacher_profile_id = ?`,
      [teacher.profile_id]
    );

    // Get teacher categories/subjects
    const [categories] = await connection.execute(
      `SELECT c.id, c.name
       FROM teacher_categories tc
       JOIN categories c ON tc.category_id = c.id
       JOIN teacher_profiles tp ON tc.teacher_profile_id = tp.id
       WHERE tp.user_id = ?`,
      [id]
    );

    // Get teacher reviews with replies
    const [reviews] = await connection.execute(
      `SELECT
        r.id,
        r.rating,
        r.comment,
        r.created_at,
        u.full_name as student_name,
        u.profile_picture_url as student_profile_picture,
        rr.reply_text,
        rr.created_at as reply_created_at
       FROM reviews r
       JOIN users u ON r.student_id = u.id
       JOIN teacher_profiles tp ON r.teacher_profile_id = tp.id
       LEFT JOIN review_replies rr ON r.id = rr.review_id
       WHERE tp.user_id = ?
       ORDER BY r.created_at DESC
       LIMIT 5`,
      [id]
    );

    // Get language names for teaching languages
    const teachingLanguagesIds = JSON.parse(teacher.teaching_languages || '[]');
    let languageNames = [];

    if (teachingLanguagesIds.length > 0) {
      const placeholders = teachingLanguagesIds.map(() => '?').join(',');
      const [languages] = await connection.execute(
        `SELECT id, name FROM languages WHERE id IN (${placeholders})`,
        teachingLanguagesIds
      );

      // Create a map of id to name
      const languageMap = {};
      languages.forEach(lang => {
        languageMap[lang.id] = lang.name;
      });

      // Map IDs to names
      languageNames = teachingLanguagesIds.map(langId =>
        languageMap[langId] || `Language ID: ${langId}`
      );
    }

    // Get category names for course types
    const courseTypesIds = JSON.parse(teacher.course_types || '[]');
    let courseTypeNames = [];

    if (courseTypesIds.length > 0) {
      const placeholders = courseTypesIds.map(() => '?').join(',');
      const [courseTypes] = await connection.execute(
        `SELECT id, name FROM categories WHERE id IN (${placeholders})`,
        courseTypesIds
      );

      // Create a map of id to name
      const courseTypeMap = {};
      courseTypes.forEach(type => {
        courseTypeMap[type.id] = type.name;
      });

      // Map IDs to names
      courseTypeNames = courseTypesIds.map(typeId =>
        courseTypeMap[typeId] || `Course Type ID: ${typeId}`
      );
    }

    // Format the response
    const formattedTeacher = {
      id: teacher.id,
      full_name: teacher.full_name,
      email: teacher.email,
      gender: teacher.gender,
      profile_picture_url: teacher.profile_picture_url || teacher.user_profile_picture,
      user_created_at: teacher.user_created_at,
      profile_id: teacher.profile_id,
      phone: teacher.phone,
      country: teacher.country,
      residence: teacher.residence,
      native_language: teacher.native_language,
      teaching_languages: languageNames, // Use language names instead of IDs
      teaching_languages_ids: JSON.parse(teacher.teaching_languages || '[]'), // Keep IDs for reference
      course_types: courseTypeNames, // Use category names instead of IDs
      course_types_ids: JSON.parse(teacher.course_types || '[]'), // Keep IDs for reference
      qualifications: teacher.qualifications,
      teaching_experience: teacher.teaching_experience,
      intro_video_url: teacher.intro_video_url,
      cv: teacher.cv,
      available_hours: (() => {
        const originalHours = JSON.parse(teacher.available_hours || '{}');



        // If no breaks, return original hours
        if (weeklyBreaks.length === 0) {
          return originalHours;
        }

        // Get teacher timezone for conversion
        const teacherTimezone = teacher.timezone || 'UTC';
        const timezoneMatch = teacherTimezone.match(/UTC([+-])(\d{2}):(\d{2})/);
        let offsetMinutes = 0;
        if (timezoneMatch) {
          const sign = timezoneMatch[1] === '+' ? 1 : -1;
          const hours = parseInt(timezoneMatch[2]);
          const minutes = parseInt(timezoneMatch[3]);
          offsetMinutes = sign * (hours * 60 + minutes);
        }

        // Create a set of break slots for quick lookup
        const breakSlots = new Set();
        weeklyBreaks.forEach(breakSlot => {
          // Parse the UTC datetime from database
          const utcDateTime = new Date(breakSlot.datetime);

          if (isNaN(utcDateTime.getTime())) {
            console.error('Invalid break datetime:', breakSlot);
            return;
          }

          // Convert UTC time back to teacher timezone for display
          const teacherDateTime = new Date(utcDateTime.getTime() + offsetMinutes * 60 * 1000);

          const dayOfWeek = teacherDateTime.getDay(); // 0 = Sunday, 1 = Monday, etc.
          const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
          const dayName = dayNames[dayOfWeek];

          const teacherHour = teacherDateTime.getHours();
          const teacherMinute = teacherDateTime.getMinutes();
          const timeSlot = `${teacherHour.toString().padStart(2, '0')}:${teacherMinute.toString().padStart(2, '0')}`;
          const breakKey = `${dayName}_${timeSlot}`;
          breakSlots.add(breakKey);
        });

        // Filter out break slots from available hours
        const filteredHours = {};
        Object.keys(originalHours).forEach(day => {
          if (Array.isArray(originalHours[day])) {
            const originalSlots = originalHours[day];
            filteredHours[day] = originalSlots.filter(timeSlot => {
              const [startTime] = timeSlot.split('-');
              const breakKey = `${day}_${startTime}`;
              const isBreak = breakSlots.has(breakKey);
              return !isBreak;
            });
          } else {
            filteredHours[day] = originalHours[day];
          }
        });

        return filteredHours;
      })(),
      price_per_lesson: teacher.price_per_lesson,
       trial_lesson_price: teacher.trial_lesson_price,
      timezone: teacher.timezone,
      payment_method: teacher.payment_method,
      status: teacher.status,
      profile_created_at: teacher.profile_created_at,
      profile_updated_at: teacher.profile_updated_at,
      average_rating: parseFloat(teacher.average_rating) || 0,
      review_count: teacher.review_count,
      subjects: categories.map(c => c.name),
      reviews: reviews
    };

    // Add debug header
    res.setHeader('X-Breaks-Count', weeklyBreaks.length);

    return res.json({
      success: true,
      data: formattedTeacher
    });

  } catch (error) {
    console.error('Error getting teacher by ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting teacher information'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

/**
 * Get teacher by profile ID
 */
const getTeacherByProfileId = async (req, res) => {
  let connection;
  try {
    const { profileId } = req.params;

    connection = await mysql.createConnection(config);

    // Get teacher profile with user data
    const [teachers] = await connection.execute(
      `SELECT
        u.id,
        u.full_name,
        u.email,
        u.gender,
        u.profile_picture_url as user_profile_picture,
        u.created_at as user_created_at,
        tp.id as profile_id,
        tp.user_id,
        tp.phone,
        tp.country,
        tp.residence,
        tp.native_language,
        tp.teaching_languages,
        tp.course_types,
        tp.qualifications,
        tp.teaching_experience,
        tp.intro_video_url,
        tp.cv,
        tp.profile_picture_url,
        tp.available_hours,
        tp.price_per_lesson,
        tp.trial_lesson_price,
        tp.timezone,
        tp.payment_method,
        tp.status,
        tp.created_at as profile_created_at,
        tp.updated_at as profile_updated_at,
        COALESCE(AVG(r.rating), 0) as average_rating,
        COUNT(DISTINCT r.id) as review_count
      FROM teacher_profiles tp
      JOIN users u ON tp.user_id = u.id
      LEFT JOIN reviews r ON tp.id = r.teacher_profile_id
      WHERE tp.id = ? AND u.role = 'platform_teacher' AND tp.status = 'approved'
      GROUP BY tp.id`,
      [profileId]
    );

    if (teachers.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const teacher = teachers[0];

    // Get current week's breaks for this teacher
    const today = new Date();
    const currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay() + 1); // Monday
    const currentWeekEnd = new Date(currentWeekStart);
    currentWeekEnd.setDate(currentWeekStart.getDate() + 6); // Sunday

    const [weeklyBreaks] = await connection.execute(
      `SELECT datetime FROM teacher_weekly_breaks
       WHERE teacher_profile_id = ?
       AND datetime >= ?`,
      [profileId, today.toISOString()]
    );

    // Get teacher categories/subjects
    const [categories] = await connection.execute(
      `SELECT c.id, c.name
       FROM teacher_categories tc
       JOIN categories c ON tc.category_id = c.id
       WHERE tc.teacher_profile_id = ?`,
      [profileId]
    );

    // Get teacher reviews with replies
    const [reviews] = await connection.execute(
      `SELECT
        r.id,
        r.rating,
        r.comment,
        r.created_at,
        u.full_name as student_name,
        u.profile_picture_url as student_profile_picture,
        rr.reply_text,
        rr.created_at as reply_created_at
       FROM reviews r
       JOIN users u ON r.student_id = u.id
       LEFT JOIN review_replies rr ON r.id = rr.review_id
       WHERE r.teacher_profile_id = ?
       ORDER BY r.created_at DESC
       LIMIT 5`,
      [profileId]
    );

    // Get language names for teaching languages
    const teachingLanguagesIds = JSON.parse(teacher.teaching_languages || '[]');
    let languageNames = [];

    if (teachingLanguagesIds.length > 0) {
      const placeholders = teachingLanguagesIds.map(() => '?').join(',');
      const [languages] = await connection.execute(
        `SELECT id, name FROM languages WHERE id IN (${placeholders})`,
        teachingLanguagesIds
      );

      // Create a map of id to name
      const languageMap = {};
      languages.forEach(lang => {
        languageMap[lang.id] = lang.name;
      });

      // Map IDs to names
      languageNames = teachingLanguagesIds.map(langId =>
        languageMap[langId] || `Language ID: ${langId}`
      );
    }

    // Get category names for course types
    const courseTypesIds = JSON.parse(teacher.course_types || '[]');
    let courseTypeNames = [];

    if (courseTypesIds.length > 0) {
      const placeholders = courseTypesIds.map(() => '?').join(',');
      const [courseTypes] = await connection.execute(
        `SELECT id, name FROM categories WHERE id IN (${placeholders})`,
        courseTypesIds
      );

      // Create a map of id to name
      const courseTypeMap = {};
      courseTypes.forEach(type => {
        courseTypeMap[type.id] = type.name;
      });

      // Map IDs to names
      courseTypeNames = courseTypesIds.map(typeId =>
        courseTypeMap[typeId] || `Course Type ID: ${typeId}`
      );
    }

    // Format the response
    const formattedTeacher = {
      id: teacher.id,
      user_id: teacher.user_id,
      full_name: teacher.full_name,
      email: teacher.email,
      gender: teacher.gender,
      profile_picture_url: teacher.profile_picture_url || teacher.user_profile_picture,
      user_created_at: teacher.user_created_at,
      profile_id: teacher.profile_id,
      phone: teacher.phone,
      country: teacher.country,
      residence: teacher.residence,
      native_language: teacher.native_language,
      teaching_languages: languageNames, // Use language names instead of IDs
      teaching_languages_ids: JSON.parse(teacher.teaching_languages || '[]'), // Keep IDs for reference
      course_types: courseTypeNames, // Use category names instead of IDs
      course_types_ids: JSON.parse(teacher.course_types || '[]'), // Keep IDs for reference
      qualifications: teacher.qualifications,
      teaching_experience: teacher.teaching_experience,
      intro_video_url: teacher.intro_video_url,
      cv: teacher.cv,
      available_hours: (() => {
        const originalHours = JSON.parse(teacher.available_hours || '{}');

        // Get teacher timezone for conversion
        const teacherTimezone = teacher.timezone || 'UTC';
        const timezoneMatch = teacherTimezone.match(/UTC([+-])(\d{2}):(\d{2})/);
        let offsetMinutes = 0;
        if (timezoneMatch) {
          const sign = timezoneMatch[1] === '+' ? 1 : -1;
          const hours = parseInt(timezoneMatch[2]);
          const minutes = parseInt(timezoneMatch[3]);
          offsetMinutes = sign * (hours * 60 + minutes);
        }

        // Create a set of break slots for quick lookup
        const breakSlots = new Set();
        weeklyBreaks.forEach(breakSlot => {
          // Parse the UTC datetime from database
          const utcDateTime = new Date(breakSlot.datetime);

          if (isNaN(utcDateTime.getTime())) {
            console.error('Invalid break datetime:', breakSlot);
            return;
          }

          // Convert UTC time back to teacher timezone for display
          const teacherDateTime = new Date(utcDateTime.getTime() + offsetMinutes * 60 * 1000);

          const dayOfWeek = teacherDateTime.getDay(); // 0 = Sunday, 1 = Monday, etc.
          const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
          const dayName = dayNames[dayOfWeek];

          const teacherHour = teacherDateTime.getHours();
          const teacherMinute = teacherDateTime.getMinutes();
          const timeSlot = `${teacherHour.toString().padStart(2, '0')}:${teacherMinute.toString().padStart(2, '0')}`;
          const breakKey = `${dayName}_${timeSlot}`;
          breakSlots.add(breakKey);
        });

        // Filter out break slots from available hours
        const filteredHours = {};
        Object.keys(originalHours).forEach(day => {
          if (Array.isArray(originalHours[day])) {
            filteredHours[day] = originalHours[day].filter(timeSlot => {
              const [startTime] = timeSlot.split('-');
              const breakKey = `${day}_${startTime}`;
              return !breakSlots.has(breakKey);
            });
          } else {
            filteredHours[day] = originalHours[day];
          }
        });

        return filteredHours;
      })(),
      price_per_lesson: teacher.price_per_lesson,
       trial_lesson_price: teacher.trial_lesson_price,
      timezone: teacher.timezone,
      payment_method: teacher.payment_method,
      status: teacher.status,
      profile_created_at: teacher.profile_created_at,
      profile_updated_at: teacher.profile_updated_at,
      average_rating: parseFloat(teacher.average_rating) || 0,
      review_count: teacher.review_count,
      subjects: categories.map(c => c.name),
      reviews: reviews
    };

    return res.json({
      success: true,
      data: formattedTeacher
    });

  } catch (error) {
    console.error('Error getting teacher by profile ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting teacher information'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

// Get teacher available slots with booking conflicts
const getTeacherAvailableSlots = async (req, res) => {
  const { id } = req.params;

  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Get teacher profile with available hours
    const [teachers] = await connection.execute(
      `SELECT tp.id as profile_id, tp.available_hours, tp.timezone, tp.price_per_lesson
       FROM teacher_profiles tp
       JOIN users u ON tp.user_id = u.id
       WHERE u.id = ? AND tp.status = 'approved'`,
      [id]
    );

    if (teachers.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found or not approved'
      });
    }

    const teacher = teachers[0];
    const availableHours = JSON.parse(teacher.available_hours || '{}');

    // Get existing bookings for the next 30 days
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);

    const [bookings] = await connection.execute(
      `SELECT datetime, duration FROM bookings
       WHERE teacher_profile_id = ?
       AND datetime >= ?
       AND datetime <= ?
       AND status != 'cancelled'`,
      [teacher.profile_id, today.toISOString(), thirtyDaysFromNow.toISOString()]
    );

    // Get teacher's weekly breaks for the next 30 days
    const [weeklyBreaks] = await connection.execute(
      `SELECT datetime FROM teacher_weekly_breaks
       WHERE teacher_profile_id = ?
       AND datetime >= ?
       AND datetime <= ?`,
      [teacher.profile_id, today.toISOString(), thirtyDaysFromNow.toISOString()]
    );

    // Create a set of booked slots for quick lookup
    const bookedSlots = new Set();
    bookings.forEach(booking => {
      const bookingDate = new Date(booking.datetime);
      const duration = parseInt(booking.duration) || 50;

      // Add the booked slot
      const slotKey = `${bookingDate.toISOString().split('T')[0]}_${bookingDate.getHours().toString().padStart(2, '0')}:${bookingDate.getMinutes().toString().padStart(2, '0')}`;
      bookedSlots.add(slotKey);

      // If it's a 50-minute booking, also block the next 30-minute slot
      if (duration === 50) {
        const nextSlotTime = new Date(bookingDate.getTime() + 30 * 60 * 1000);
        const nextSlotKey = `${nextSlotTime.toISOString().split('T')[0]}_${nextSlotTime.getHours().toString().padStart(2, '0')}:${nextSlotTime.getMinutes().toString().padStart(2, '0')}`;
        bookedSlots.add(nextSlotKey);
      }
    });

    // Get teacher timezone for conversion
    const teacherTimezone = teacher.timezone || 'UTC';
    const timezoneMatch = teacherTimezone.match(/UTC([+-])(\d{2}):(\d{2})/);
    let offsetMinutes = 0;
    if (timezoneMatch) {
      const sign = timezoneMatch[1] === '+' ? 1 : -1;
      const hours = parseInt(timezoneMatch[2]);
      const minutes = parseInt(timezoneMatch[3]);
      offsetMinutes = sign * (hours * 60 + minutes);
    }

    // Add weekly breaks to booked slots
    weeklyBreaks.forEach(breakSlot => {
      // Parse the UTC datetime from database
      const utcDateTime = new Date(breakSlot.datetime);

      if (isNaN(utcDateTime.getTime())) {
        console.error('Invalid break datetime:', breakSlot);
        return;
      }

      // Convert UTC time back to teacher timezone for comparison
      const teacherDateTime = new Date(utcDateTime.getTime() + offsetMinutes * 60 * 1000);

      // Format as date_hour:minute for comparison with booking slots
      const breakDate = teacherDateTime.toISOString().split('T')[0];
      const breakHour = teacherDateTime.getHours();
      const breakMinute = teacherDateTime.getMinutes();
      const breakKey = `${breakDate}_${breakHour.toString().padStart(2, '0')}:${breakMinute.toString().padStart(2, '0')}`;
      bookedSlots.add(breakKey);
    });

    // Generate available slots for the next 30 days
    const availableSlots = [];
    const daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

    for (let dayOffset = 0; dayOffset < 30; dayOffset++) {
      const currentDate = new Date(today);
      currentDate.setDate(today.getDate() + dayOffset);
      const dayName = daysOfWeek[currentDate.getDay()];
      const dateString = currentDate.toISOString().split('T')[0];

      if (availableHours[dayName] && availableHours[dayName].length > 0) {
        availableHours[dayName].forEach(timeSlot => {
          const [startTime, endTime] = timeSlot.split('-');
          const [startHour, startMinute] = startTime.split(':').map(Number);

          const slotDateTime = new Date(currentDate);
          slotDateTime.setHours(startHour, startMinute, 0, 0);

          // Skip past slots
          if (slotDateTime <= new Date()) {
            return;
          }

          const slotKey = `${dateString}_${startTime}`;

          if (!bookedSlots.has(slotKey)) {
            // Check if this can be part of flexible booking options
            const flexibleOptions = getFlexibleBookingOptions(
              dateString, startTime, availableHours[dayName], bookedSlots, dayOffset, today
            );

            availableSlots.push({
              date: dateString,
              time: startTime,
              endTime: endTime,
              datetime: slotDateTime.toISOString(),
              dayName: dayName,
              isAvailable: true,
              flexibleOptions: flexibleOptions
            });
          }
        });
      }
    }

    res.json({
      success: true,
      data: availableSlots,
      teacher: {
        id: id,
        profile_id: teacher.profile_id,
        timezone: teacher.timezone,
        price_per_lesson: teacher.price_per_lesson
      }
    });

  } catch (error) {
    console.error('Error fetching teacher available slots:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching available slots'
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Helper function to determine flexible booking options
const getFlexibleBookingOptions = (dateString, startTime, daySlots, bookedSlots, dayOffset, today) => {
  const options = {
    canBookSecondHalf: false,
    canBookCrossHour: false,
    crossHourEndTime: null
  };

  const [startHour, startMinute] = startTime.split(':').map(Number);

  // Check if this is the start of an hour (XX:00) and next slot (XX:30) is also available
  if (startMinute === 0) {
    const nextHalfSlot = `${startHour.toString().padStart(2, '0')}:30-${(startHour + 1).toString().padStart(2, '0')}:00`;
    const nextHalfKey = `${dateString}_${startHour.toString().padStart(2, '0')}:30`;

    if (daySlots.includes(nextHalfSlot) && !bookedSlots.has(nextHalfKey)) {
      options.canBookSecondHalf = true;
    }
  }

  // Check if this is the second half of an hour (XX:30) and can be combined with next hour's first half
  if (startMinute === 30) {
    const nextHourFirstHalf = `${(startHour + 1).toString().padStart(2, '0')}:00-${(startHour + 1).toString().padStart(2, '0')}:30`;
    const nextHourKey = `${dateString}_${(startHour + 1).toString().padStart(2, '0')}:00`;

    if (daySlots.includes(nextHourFirstHalf) && !bookedSlots.has(nextHourKey)) {
      options.canBookCrossHour = true;
      options.crossHourEndTime = `${(startHour + 1).toString().padStart(2, '0')}:30`;
    }
  }

  return options;
};

module.exports = {
  getTeacherById,
  getTeacherByProfileId,
  getTeacherAvailableSlots
};
