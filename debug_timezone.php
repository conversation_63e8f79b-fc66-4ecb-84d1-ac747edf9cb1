<?php
/**
 * Simple Timezone Debug Page
 * Direct database access without any framework dependencies
 */

// Database configuration - update these with your actual database credentials
$host = 'localhost';
$dbname = 'allemnionline';
$username = 'allemnionline';  // Try this if root doesn't work
$password = 'allemnionline';  // Try this if empty doesn't work

// Alternative configurations to try:
// For XAMPP: username='root', password=''
// For production: username='allemnionline', password='your_password'

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timezone Debug - Teacher Weekly Breaks</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .section h3 { color: #666; margin-top: 20px; }
        .data-row { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 3px; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 3px; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 3px; }
        .step { background: #e9ecef; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; font-weight: bold; }
        .highlight { background: #fff3cd; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Timezone Debug - Teacher Weekly Breaks</h1>
        <p>This page helps diagnose timezone conversion issues in the teacher weekly breaks system.</p>
        
        <?php
        try {
            // Create database connection
            $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "<div class='success'>✅ Database connection successful!</div>";
            
            // Get teacher profile ID 32 (from the screenshot)
            $teacherProfileId = 32;
            
            // Section 1: Raw Database Data
            echo "<div class='section'>";
            echo "<h2>📊 1. Raw Database Data</h2>";
            
            // Get teacher info
            $stmt = $pdo->prepare("
                SELECT tp.id, tp.timezone, u.full_name 
                FROM teacher_profiles tp 
                JOIN users u ON tp.user_id = u.id 
                WHERE tp.id = ?
            ");
            $stmt->execute([$teacherProfileId]);
            $teacher = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($teacher) {
                echo "<div class='info'>";
                echo "<strong>Teacher:</strong> {$teacher['full_name']}<br>";
                echo "<strong>Profile ID:</strong> {$teacher['id']}<br>";
                echo "<strong>Timezone:</strong> {$teacher['timezone']}";
                echo "</div>";
            } else {
                echo "<div class='error'>Teacher with profile ID {$teacherProfileId} not found!</div>";
                echo "</div></div></body></html>";
                exit;
            }
            
            // Get all breaks for this teacher
            $stmt = $pdo->prepare("
                SELECT id, datetime, created_at 
                FROM teacher_weekly_breaks 
                WHERE teacher_profile_id = ? 
                ORDER BY datetime
            ");
            $stmt->execute([$teacherProfileId]);
            $breaks = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>📅 Weekly Breaks in Database:</h3>";
            if (empty($breaks)) {
                echo "<div class='warning'>No breaks found for this teacher.</div>";
            } else {
                echo "<table>";
                echo "<tr><th>ID</th><th>DateTime (UTC)</th><th>Created At</th></tr>";
                foreach ($breaks as $break) {
                    echo "<tr>";
                    echo "<td>{$break['id']}</td>";
                    echo "<td class='highlight'>{$break['datetime']}</td>";
                    echo "<td>{$break['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            echo "</div>";
            
            // Section 2: Timezone Parsing
            echo "<div class='section'>";
            echo "<h2>⚙️ 2. Timezone Parsing</h2>";
            
            $teacherTimezone = $teacher['timezone'];
            echo "<div class='step'>";
            echo "<strong>Teacher Timezone:</strong> {$teacherTimezone}<br>";
            
            // Parse timezone offset
            if (preg_match('/UTC([+-])(\d{2}):(\d{2})/', $teacherTimezone, $matches)) {
                $sign = $matches[1] === '+' ? 1 : -1;
                $hours = intval($matches[2]);
                $minutes = intval($matches[3]);
                $offsetMinutes = $sign * ($hours * 60 + $minutes);
                
                echo "<strong>Parsed Successfully:</strong><br>";
                echo "- Sign: {$matches[1]}<br>";
                echo "- Hours: {$hours}<br>";
                echo "- Minutes: {$minutes}<br>";
                echo "- Total Offset Minutes: {$offsetMinutes}<br>";
                
                if ($offsetMinutes > 0) {
                    echo "- This means teacher is {$offsetMinutes} minutes AHEAD of UTC";
                } else {
                    echo "- This means teacher is " . abs($offsetMinutes) . " minutes BEHIND UTC";
                }
            } else {
                echo "<div class='error'>Failed to parse timezone: {$teacherTimezone}</div>";
                $offsetMinutes = 0;
            }
            echo "</div>";
            echo "</div>";
            
            // Section 3: Current Time Analysis
            echo "<div class='section'>";
            echo "<h2>🕐 3. Current Time Analysis</h2>";
            
            $currentUtc = gmdate('Y-m-d H:i:s');
            $currentLocal = date('Y-m-d H:i:s');
            $currentTeacher = gmdate('Y-m-d H:i:s', time() + ($offsetMinutes * 60));
            
            echo "<div class='step'>";
            echo "<strong>🌍 Current UTC:</strong> {$currentUtc}<br>";
            echo "<strong>🖥️ Server Local:</strong> {$currentLocal}<br>";
            echo "<strong>🏠 Teacher Time:</strong> {$currentTeacher}<br>";
            echo "</div>";
            echo "</div>";
            
            // Section 4: Conversion Analysis
            if (!empty($breaks)) {
                echo "<div class='section'>";
                echo "<h2>🔄 4. Timezone Conversion Analysis</h2>";
                
                foreach ($breaks as $index => $break) {
                    echo "<div class='step'>";
                    echo "<h3>Break #" . ($index + 1) . " (ID: {$break['id']})</h3>";
                    
                    $utcDatetime = $break['datetime'];
                    echo "<strong>📊 Raw from DB:</strong> {$utcDatetime}<br>";
                    
                    // Parse UTC datetime
                    $utcTimestamp = strtotime($utcDatetime);
                    if ($utcTimestamp === false) {
                        echo "<div class='error'>Invalid datetime format!</div>";
                        continue;
                    }
                    
                    echo "<strong>🌍 UTC Timestamp:</strong> {$utcTimestamp}<br>";
                    echo "<strong>🌍 UTC ISO:</strong> " . gmdate('Y-m-d H:i:s', $utcTimestamp) . "<br>";
                    
                    // Convert to teacher timezone
                    $teacherTimestamp = $utcTimestamp + ($offsetMinutes * 60);
                    $teacherDatetime = gmdate('Y-m-d H:i:s', $teacherTimestamp);
                    
                    echo "<strong>🏠 Teacher Timestamp:</strong> {$teacherTimestamp}<br>";
                    echo "<strong>🏠 Teacher DateTime:</strong> {$teacherDatetime}<br>";
                    
                    // Break down teacher datetime
                    $teacherDate = gmdate('Y-m-d', $teacherTimestamp);
                    $teacherTime = gmdate('H:i', $teacherTimestamp);
                    $teacherHour = gmdate('H', $teacherTimestamp);
                    $teacherMinute = gmdate('i', $teacherTimestamp);
                    $teacherDayName = gmdate('l', $teacherTimestamp);
                    
                    echo "<strong>📅 Teacher Date:</strong> {$teacherDate}<br>";
                    echo "<strong>⏰ Teacher Time:</strong> {$teacherTime}<br>";
                    echo "<strong>📍 Teacher Day:</strong> {$teacherDayName}<br>";
                    
                    // Show what the frontend should see
                    echo "<div class='info'>";
                    echo "<strong>🎯 Expected Frontend Result:</strong><br>";
                    echo "- Should hide slot on: <strong>{$teacherDate}</strong><br>";
                    echo "- At time: <strong>{$teacherTime}</strong><br>";
                    echo "- Day: <strong>{$teacherDayName}</strong>";
                    echo "</div>";
                    
                    echo "</div>";
                }
                echo "</div>";
            }
            
            // Section 5: What's happening in the website
            echo "<div class='section'>";
            echo "<h2>🌐 5. What Should Happen in the Website</h2>";
            
            if (!empty($breaks)) {
                foreach ($breaks as $index => $break) {
                    $utcTimestamp = strtotime($break['datetime']);
                    $teacherTimestamp = $utcTimestamp + ($offsetMinutes * 60);
                    $teacherDate = gmdate('Y-m-d', $teacherTimestamp);
                    $teacherTime = gmdate('H:i', $teacherTimestamp);
                    $teacherDayName = gmdate('l', $teacherTimestamp);
                    
                    echo "<div class='step'>";
                    echo "<h3>Break #" . ($index + 1) . ":</h3>";
                    echo "<strong>❌ This slot should be HIDDEN:</strong><br>";
                    echo "- Date: {$teacherDate}<br>";
                    echo "- Day: {$teacherDayName}<br>";
                    echo "- Time: {$teacherTime}<br>";
                    echo "<br>";
                    echo "<strong>✅ All other slots should be VISIBLE</strong>";
                    echo "</div>";
                }
            }
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h2>❌ Database Error</h2>";
            echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p><strong>Check your database credentials in the file.</strong></p>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>
