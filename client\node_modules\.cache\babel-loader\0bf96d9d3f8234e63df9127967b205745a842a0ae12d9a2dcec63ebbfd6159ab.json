{"ast": null, "code": "import axios from 'axios';\nconst instance = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || '',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add a request interceptor\ninstance.interceptors.request.use(config => {\n  // Get the token from localStorage\n  const token = localStorage.getItem('token');\n\n  // If token exists, add it to the headers\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n\n  // For multipart/form-data, let the browser set the Content-Type\n  if (config.data instanceof FormData) {\n    delete config.headers['Content-Type'];\n  }\n\n  // Add /api prefix to all requests except for socket.io\n  if (!config.url.startsWith('/socket.io') && !config.url.startsWith('/api')) {\n    config.url = `/api${config.url}`;\n  }\n\n  // Add a timestamp to prevent caching\n  const separator = config.url.includes('?') ? '&' : '?';\n  config.url = `${config.url}${separator}_t=${Date.now()}`;\n  console.log('Request config:', {\n    url: config.url,\n    method: config.method,\n    hasToken: !!token,\n    token: token ? `${token.substring(0, 10)}...` : null,\n    headers: config.headers\n  }); // Debug log\n\n  return config;\n}, error => {\n  console.error('Request error:', error);\n  return Promise.reject(error);\n});\n\n// Add a response interceptor\ninstance.interceptors.response.use(response => {\n  // Log successful responses for debugging\n  console.log('API Response:', {\n    url: response.config.url,\n    method: response.config.method,\n    status: response.status,\n    data: response.data\n  });\n\n  // Save teacher response for debugging\n  if (response.config.url && response.config.url.includes('/teachers/')) {\n    window.lastTeacherResponse = response.data;\n    console.log('Teacher response saved:', response.data);\n  }\n  return response;\n}, error => {\n  var _error$config, _error$config2, _error$response, _error$response2, _error$response3, _error$response4;\n  // Log error responses for debugging\n  console.error('API Error Details:', {\n    url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n    method: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.method,\n    status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n    statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n    data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n    headers: (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.headers,\n    message: error.message,\n    fullError: error\n  });\n\n  // Ensure error has response data for proper handling\n  if (error.response && error.response.data) {\n    // Error response is properly formatted\n    return Promise.reject(error);\n  } else if (error.response) {\n    // Response exists but no data, create a proper error structure\n    const enhancedError = new Error(error.response.statusText || 'Request failed');\n    enhancedError.response = {\n      ...error.response,\n      data: {\n        success: false,\n        message: error.response.statusText || 'Request failed'\n      }\n    };\n    return Promise.reject(enhancedError);\n  } else {\n    // Network error or other issue\n    const enhancedError = new Error(error.message || 'Network error');\n    enhancedError.response = {\n      status: 0,\n      data: {\n        success: false,\n        message: error.message || 'Network error'\n      }\n    };\n    return Promise.reject(enhancedError);\n  }\n});\nexport default instance;", "map": {"version": 3, "names": ["axios", "instance", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "data", "FormData", "url", "startsWith", "separator", "includes", "Date", "now", "console", "log", "method", "hasToken", "substring", "error", "Promise", "reject", "response", "status", "window", "lastTeacherResponse", "_error$config", "_error$config2", "_error$response", "_error$response2", "_error$response3", "_error$response4", "statusText", "message", "fullError", "enhancedError", "Error", "success"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/utils/axios.js"], "sourcesContent": ["import axios from 'axios';\n\nconst instance = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || '',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add a request interceptor\ninstance.interceptors.request.use(\n  (config) => {\n    // Get the token from localStorage\n    const token = localStorage.getItem('token');\n\n    // If token exists, add it to the headers\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n\n    // For multipart/form-data, let the browser set the Content-Type\n    if (config.data instanceof FormData) {\n      delete config.headers['Content-Type'];\n    }\n\n    // Add /api prefix to all requests except for socket.io\n    if (!config.url.startsWith('/socket.io') && !config.url.startsWith('/api')) {\n      config.url = `/api${config.url}`;\n    }\n\n    // Add a timestamp to prevent caching\n    const separator = config.url.includes('?') ? '&' : '?';\n    config.url = `${config.url}${separator}_t=${Date.now()}`;\n\n    console.log('Request config:', {\n      url: config.url,\n      method: config.method,\n      hasToken: !!token,\n      token: token ? `${token.substring(0, 10)}...` : null,\n      headers: config.headers\n    }); // Debug log\n\n    return config;\n  },\n  (error) => {\n    console.error('Request error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Add a response interceptor\ninstance.interceptors.response.use(\n  (response) => {\n    // Log successful responses for debugging\n    console.log('API Response:', {\n      url: response.config.url,\n      method: response.config.method,\n      status: response.status,\n      data: response.data\n    });\n\n    // Save teacher response for debugging\n    if (response.config.url && response.config.url.includes('/teachers/')) {\n      window.lastTeacherResponse = response.data;\n      console.log('Teacher response saved:', response.data);\n    }\n\n    return response;\n  },\n  (error) => {\n    // Log error responses for debugging\n    console.error('API Error Details:', {\n      url: error.config?.url,\n      method: error.config?.method,\n      status: error.response?.status,\n      statusText: error.response?.statusText,\n      data: error.response?.data,\n      headers: error.response?.headers,\n      message: error.message,\n      fullError: error\n    });\n\n    // Ensure error has response data for proper handling\n    if (error.response && error.response.data) {\n      // Error response is properly formatted\n      return Promise.reject(error);\n    } else if (error.response) {\n      // Response exists but no data, create a proper error structure\n      const enhancedError = new Error(error.response.statusText || 'Request failed');\n      enhancedError.response = {\n        ...error.response,\n        data: {\n          success: false,\n          message: error.response.statusText || 'Request failed'\n        }\n      };\n      return Promise.reject(enhancedError);\n    } else {\n      // Network error or other issue\n      const enhancedError = new Error(error.message || 'Network error');\n      enhancedError.response = {\n        status: 0,\n        data: {\n          success: false,\n          message: error.message || 'Network error'\n        }\n      };\n      return Promise.reject(enhancedError);\n    }\n  }\n);\n\nexport default instance;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,QAAQ,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC5BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,EAAE;EAC5CC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAN,QAAQ,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;EAE3C;EACA,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;;EAEA;EACA,IAAID,MAAM,CAACK,IAAI,YAAYC,QAAQ,EAAE;IACnC,OAAON,MAAM,CAACJ,OAAO,CAAC,cAAc,CAAC;EACvC;;EAEA;EACA,IAAI,CAACI,MAAM,CAACO,GAAG,CAACC,UAAU,CAAC,YAAY,CAAC,IAAI,CAACR,MAAM,CAACO,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;IAC1ER,MAAM,CAACO,GAAG,GAAG,OAAOP,MAAM,CAACO,GAAG,EAAE;EAClC;;EAEA;EACA,MAAME,SAAS,GAAGT,MAAM,CAACO,GAAG,CAACG,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;EACtDV,MAAM,CAACO,GAAG,GAAG,GAAGP,MAAM,CAACO,GAAG,GAAGE,SAAS,MAAME,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;EAExDC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;IAC7BP,GAAG,EAAEP,MAAM,CAACO,GAAG;IACfQ,MAAM,EAAEf,MAAM,CAACe,MAAM;IACrBC,QAAQ,EAAE,CAAC,CAACf,KAAK;IACjBA,KAAK,EAAEA,KAAK,GAAG,GAAGA,KAAK,CAACgB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI;IACpDrB,OAAO,EAAEI,MAAM,CAACJ;EAClB,CAAC,CAAC,CAAC,CAAC;;EAEJ,OAAOI,MAAM;AACf,CAAC,EACAkB,KAAK,IAAK;EACTL,OAAO,CAACK,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACtC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA5B,QAAQ,CAACO,YAAY,CAACwB,QAAQ,CAACtB,GAAG,CAC/BsB,QAAQ,IAAK;EACZ;EACAR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE;IAC3BP,GAAG,EAAEc,QAAQ,CAACrB,MAAM,CAACO,GAAG;IACxBQ,MAAM,EAAEM,QAAQ,CAACrB,MAAM,CAACe,MAAM;IAC9BO,MAAM,EAAED,QAAQ,CAACC,MAAM;IACvBjB,IAAI,EAAEgB,QAAQ,CAAChB;EACjB,CAAC,CAAC;;EAEF;EACA,IAAIgB,QAAQ,CAACrB,MAAM,CAACO,GAAG,IAAIc,QAAQ,CAACrB,MAAM,CAACO,GAAG,CAACG,QAAQ,CAAC,YAAY,CAAC,EAAE;IACrEa,MAAM,CAACC,mBAAmB,GAAGH,QAAQ,CAAChB,IAAI;IAC1CQ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,QAAQ,CAAChB,IAAI,CAAC;EACvD;EAEA,OAAOgB,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAO,aAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;EACT;EACAjB,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAE;IAClCX,GAAG,GAAAkB,aAAA,GAAEP,KAAK,CAAClB,MAAM,cAAAyB,aAAA,uBAAZA,aAAA,CAAclB,GAAG;IACtBQ,MAAM,GAAAW,cAAA,GAAER,KAAK,CAAClB,MAAM,cAAA0B,cAAA,uBAAZA,cAAA,CAAcX,MAAM;IAC5BO,MAAM,GAAAK,eAAA,GAAET,KAAK,CAACG,QAAQ,cAAAM,eAAA,uBAAdA,eAAA,CAAgBL,MAAM;IAC9BS,UAAU,GAAAH,gBAAA,GAAEV,KAAK,CAACG,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBG,UAAU;IACtC1B,IAAI,GAAAwB,gBAAA,GAAEX,KAAK,CAACG,QAAQ,cAAAQ,gBAAA,uBAAdA,gBAAA,CAAgBxB,IAAI;IAC1BT,OAAO,GAAAkC,gBAAA,GAAEZ,KAAK,CAACG,QAAQ,cAAAS,gBAAA,uBAAdA,gBAAA,CAAgBlC,OAAO;IAChCoC,OAAO,EAAEd,KAAK,CAACc,OAAO;IACtBC,SAAS,EAAEf;EACb,CAAC,CAAC;;EAEF;EACA,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAAChB,IAAI,EAAE;IACzC;IACA,OAAOc,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;EAC9B,CAAC,MAAM,IAAIA,KAAK,CAACG,QAAQ,EAAE;IACzB;IACA,MAAMa,aAAa,GAAG,IAAIC,KAAK,CAACjB,KAAK,CAACG,QAAQ,CAACU,UAAU,IAAI,gBAAgB,CAAC;IAC9EG,aAAa,CAACb,QAAQ,GAAG;MACvB,GAAGH,KAAK,CAACG,QAAQ;MACjBhB,IAAI,EAAE;QACJ+B,OAAO,EAAE,KAAK;QACdJ,OAAO,EAAEd,KAAK,CAACG,QAAQ,CAACU,UAAU,IAAI;MACxC;IACF,CAAC;IACD,OAAOZ,OAAO,CAACC,MAAM,CAACc,aAAa,CAAC;EACtC,CAAC,MAAM;IACL;IACA,MAAMA,aAAa,GAAG,IAAIC,KAAK,CAACjB,KAAK,CAACc,OAAO,IAAI,eAAe,CAAC;IACjEE,aAAa,CAACb,QAAQ,GAAG;MACvBC,MAAM,EAAE,CAAC;MACTjB,IAAI,EAAE;QACJ+B,OAAO,EAAE,KAAK;QACdJ,OAAO,EAAEd,KAAK,CAACc,OAAO,IAAI;MAC5B;IACF,CAAC;IACD,OAAOb,OAAO,CAACC,MAAM,CAACc,aAAa,CAAC;EACtC;AACF,CACF,CAAC;AAED,eAAe5C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}