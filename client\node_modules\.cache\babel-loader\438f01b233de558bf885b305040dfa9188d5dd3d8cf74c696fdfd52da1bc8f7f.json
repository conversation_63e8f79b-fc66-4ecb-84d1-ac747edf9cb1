{"ast": null, "code": "var _jsxFileName = \"D:\\\\xampp\\\\htdocs\\\\allemnionline\\\\client\\\\src\\\\pages\\\\teacher\\\\Bookings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Typography, Box, Paper, CircularProgress, Alert, Dialog, DialogTitle, DialogContent, DialogActions, Button, Avatar, Chip, Grid, useTheme, IconButton, Tooltip } from '@mui/material';\nimport { CalendarToday as CalendarIcon, AccessTime as TimeIcon, Person as PersonIcon, AttachMoney as MoneyIcon, ChevronLeft as ChevronLeftIcon, ChevronRight as ChevronRightIcon, VideoCall as VideoCallIcon, Cancel as CancelIcon, Close as CloseIcon } from '@mui/icons-material';\nimport axios from 'axios';\nimport { format, startOfWeek, addDays, addWeeks, subWeeks } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Layout from '../../components/Layout';\nimport WeeklyBookingsTable from '../../components/WeeklyBookingsTable';\nimport { convertFromDatabaseTime, formatDateInStudentTimezone, getCurrentTimeInTimezone, parseTimezoneOffset } from '../../utils/timezone';\nimport moment from 'moment-timezone';\nimport VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TeacherBookings = () => {\n  _s();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const {\n    token\n  } = useAuth();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n  const [bookings, setBookings] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedBooking, setSelectedBooking] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n  const [teacherProfile, setTeacherProfile] = useState(null);\n  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);\n  const [cancellingBooking, setCancellingBooking] = useState(false);\n  const [openMeeting, setOpenMeeting] = useState(false);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [availableHours, setAvailableHours] = useState(null);\n  const [weeklyBreaks, setWeeklyBreaks] = useState([]);\n\n  // Week navigation\n  const [currentWeekStart, setCurrentWeekStart] = useState(() => {\n    const today = new Date();\n    return startOfWeek(today, {\n      weekStartsOn: 1\n    }); // Start from current week\n  });\n\n  // Days of the week (format expected by WeeklyBookingsTable)\n  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n\n  // Week navigation functions\n  const goToPreviousWeek = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    setCurrentWeekStart(previousWeek);\n  };\n  const goToNextWeek = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, {\n      weekStartsOn: 1\n    });\n\n    // Don't allow going beyond one year ahead\n    if (nextWeek <= maxWeek) {\n      setCurrentWeekStart(nextWeek);\n    }\n  };\n\n  // Check if navigation buttons should be disabled\n  const isPreviousWeekDisabled = () => false;\n  const isNextWeekDisabled = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, {\n      weekStartsOn: 1\n    });\n    return nextWeek > maxWeek;\n  };\n\n  // Update current time every second\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Fetch bookings\n  useEffect(() => {\n    const fetchBookings = async () => {\n      try {\n        setLoading(true);\n        const {\n          data\n        } = await axios.get('/api/bookings/teacher', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        if (data.success) {\n          console.log('Teacher bookings data:', data.data);\n\n          // Make sure all bookings have the correct data types\n          const processedBookings = data.data.map(booking => ({\n            ...booking,\n            price_per_lesson: parseFloat(booking.price_per_lesson || 0),\n            duration: booking.duration ? String(booking.duration) : '50'\n          }));\n          console.log('Processed teacher bookings:', processedBookings);\n          setBookings(processedBookings);\n\n          // Set teacher profile with timezone information\n          if (data.teacherTimezone) {\n            setTeacherProfile({\n              timezone: data.teacherTimezone\n            });\n          }\n\n          // Fetch teacher's available hours\n          fetchAvailableHours();\n\n          // Fetch weekly breaks\n          fetchWeeklyBreaks();\n        } else {\n          setError(data.message || t('bookings.fetchError'));\n        }\n      } catch (error) {\n        var _error$response, _error$response$data;\n        console.error('Error fetching teacher bookings:', error);\n        setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || t('bookings.fetchError'));\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (token) {\n      fetchBookings();\n    }\n  }, [token, t]);\n\n  // Fetch weekly breaks when week changes\n  useEffect(() => {\n    if (token) {\n      fetchWeeklyBreaks();\n    }\n  }, [currentWeekStart, token]);\n\n  // Fetch available hours\n  const fetchAvailableHours = async () => {\n    try {\n      const {\n        data\n      } = await axios.get('/api/teacher/profile', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (data.success && data.profile.available_hours) {\n        const parsedHours = typeof data.profile.available_hours === 'string' ? JSON.parse(data.profile.available_hours) : data.profile.available_hours;\n        console.log('Available hours loaded:', parsedHours);\n        setAvailableHours(parsedHours);\n      }\n    } catch (error) {\n      console.error('Error fetching available hours:', error);\n    }\n  };\n\n  // Fetch weekly breaks\n  const fetchWeeklyBreaks = async () => {\n    try {\n      // Use teacher's timezone instead of UTC\n      const year = currentWeekStart.getFullYear();\n      const month = String(currentWeekStart.getMonth() + 1).padStart(2, '0');\n      const day = String(currentWeekStart.getDate()).padStart(2, '0');\n      const weekStart = `${year}-${month}-${day}`;\n      const {\n        data\n      } = await axios.get(`/api/teacher/weekly-breaks?week_start=${weekStart}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (data.success) {\n        console.log('🔍 FRONTEND: Weekly breaks received from server:', data.data);\n        console.log('🔍 FRONTEND: Setting weeklyBreaks state to:', data.data);\n        setWeeklyBreaks(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching weekly breaks:', error);\n    }\n  };\n\n  // Handle take break\n  const handleTakeBreak = async breakSlot => {\n    try {\n      // Use teacher's timezone for both date and week_start\n      const breakDate = breakSlot.date;\n      const breakDateStr = `${breakDate.getFullYear()}-${String(breakDate.getMonth() + 1).padStart(2, '0')}-${String(breakDate.getDate()).padStart(2, '0')}`;\n      const weekStartStr = `${currentWeekStart.getFullYear()}-${String(currentWeekStart.getMonth() + 1).padStart(2, '0')}-${String(currentWeekStart.getDate()).padStart(2, '0')}`;\n      const breakData = {\n        date: breakDateStr,\n        hour: breakSlot.hour,\n        minute: breakSlot.minute,\n        week_start: weekStartStr\n      };\n      const {\n        data\n      } = await axios.post('/api/teacher/take-break', breakData, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (data.success) {\n        // Add to weekly breaks list\n        setWeeklyBreaks(prev => [...prev, `${breakData.date}_${breakData.hour}:${breakData.minute.toString().padStart(2, '0')}`]);\n\n        // Show success message\n        alert(t('bookings.breakTakenSuccess', 'تم أخذ الراحة بنجاح'));\n      }\n    } catch (error) {\n      console.error('Error taking break:', error);\n      alert(t('bookings.breakTakenError', 'خطأ في أخذ الراحة'));\n    }\n  };\n\n  // Handle view details\n  const handleViewDetails = booking => {\n    setSelectedBooking(booking);\n    setDetailsDialogOpen(true);\n  };\n\n  // Handle cancel booking\n  const handleCancelBookingClick = booking => {\n    setSelectedBooking(booking);\n    setCancelDialogOpen(true);\n  };\n\n  // Handle join meeting\n  const handleJoinMeeting = async booking => {\n    try {\n      // Check if room_name exists from the booking data\n      if (!booking.room_name) {\n        console.error('No room_name found for booking:', booking);\n        alert(t('meetings.noRoomError') || 'Meeting room not found');\n        return;\n      }\n\n      // Check if meeting_id exists\n      if (!booking.meeting_id) {\n        console.error('No meeting_id found for booking:', booking);\n        alert(t('meetings.noMeetingError') || 'Meeting ID not found');\n        return;\n      }\n      console.log('Joining meeting with data:', {\n        room_name: booking.room_name,\n        meeting_id: booking.meeting_id,\n        datetime: booking.datetime,\n        duration: booking.duration\n      });\n\n      // Validate room\n      const response = await axios.get(`/meetings/${booking.room_name}/validate`);\n      setCurrentMeeting({\n        ...booking,\n        room_name: booking.room_name\n      });\n      setOpenMeeting(true);\n    } catch (error) {\n      console.error('Error joining meeting:', error);\n      alert(t('meetings.joinError'));\n    }\n  };\n  const handleCloseMeeting = () => {\n    setOpenMeeting(false);\n    setCurrentMeeting(null);\n  };\n\n  // Get meeting status from database directly\n  const getMeetingStatus = booking => {\n    return booking.status || 'scheduled';\n  };\n\n  // Check if user can join meeting\n  const canJoinMeeting = booking => {\n    if (!booking || !teacherProfile) return false;\n    const currentStatus = getMeetingStatus(booking);\n    if (currentStatus === 'cancelled' || currentStatus === 'completed') {\n      return false;\n    }\n    const meetingStartTime = new Date(booking.datetime);\n    const meetingEndTime = new Date(booking.datetime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));\n    const now = new Date();\n    return now >= meetingStartTime && now < meetingEndTime;\n  };\n\n  // Handle booking cancellation\n  const handleCancelBooking = async () => {\n    if (!selectedBooking) return;\n    try {\n      setCancellingBooking(true);\n      const {\n        data\n      } = await axios.put(`/bookings/${selectedBooking.id}/cancel`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (data.success) {\n        // Update the booking status in the local state\n        setBookings(prevBookings => prevBookings.map(booking => booking.id === selectedBooking.id ? {\n          ...booking,\n          status: 'cancelled'\n        } : booking));\n        alert(t('bookings.cancelSuccess'));\n      } else {\n        alert(data.message || t('bookings.cancelError'));\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error cancelling booking:', error);\n      alert(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || t('bookings.cancelError'));\n    } finally {\n      setCancellingBooking(false);\n      setCancelDialogOpen(false);\n      setDetailsDialogOpen(false);\n      setSelectedBooking(null);\n    }\n  };\n\n  // Get status chip color\n  const getStatusColor = status => {\n    switch (status) {\n      case 'scheduled':\n        return 'primary';\n      case 'completed':\n        return 'success';\n      case 'cancelled':\n        return 'error';\n      case 'issue_reported':\n        return 'warning';\n      case 'ongoing':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n\n  // Get translated status text\n  const getStatusText = status => {\n    return t(`bookings.statusValues.${status}`, {\n      defaultValue: status.charAt(0).toUpperCase() + status.slice(1)\n    });\n  };\n\n  // Format booking date in teacher's timezone\n  const formatBookingDate = datetime => {\n    if (!teacherProfile || !teacherProfile.timezone) {\n      return format(new Date(datetime), 'PPP', {\n        locale: isRtl ? ar : enUS\n      });\n    }\n    const formattedDate = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD');\n    return moment(formattedDate, 'YYYY-MM-DD').format('MMMM D, YYYY');\n  };\n\n  // Format booking time in teacher's timezone\n  const formatBookingTime = datetime => {\n    if (!teacherProfile || !teacherProfile.timezone) {\n      return format(new Date(datetime), 'p', {\n        locale: isRtl ? ar : enUS\n      });\n    }\n    const formattedDateTime = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    return moment(formattedDateTime, 'YYYY-MM-DD HH:mm:ss').format('h:mm A');\n  };\n\n  // Calculate lesson price based on duration\n  const calculateLessonPrice = (pricePerLesson, duration) => {\n    const durationNum = parseInt(duration, 10);\n    return durationNum === 50 ? pricePerLesson : pricePerLesson / 2;\n  };\n\n  // Render details dialog\n  const renderDetailsDialog = () => {\n    if (!selectedBooking) return null;\n    const lessonPrice = calculateLessonPrice(selectedBooking.price_per_lesson, selectedBooking.duration);\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: () => setDetailsDialogOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'primary.main',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: t('bookings.bookingDetails')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: selectedBooking.student_picture,\n                alt: selectedBooking.student_name,\n                sx: {\n                  mr: 2,\n                  width: 56,\n                  height: 56\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: selectedBooking.student_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: selectedBooking.student_email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                color: \"text.secondary\",\n                children: t('bookings.date')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: formatBookingDate(selectedBooking.datetime)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(TimeIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                color: \"text.secondary\",\n                children: t('bookings.time')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: formatBookingTime(selectedBooking.datetime)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(TimeIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                color: \"text.secondary\",\n                children: t('bookings.duration')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [selectedBooking.duration, \" \", t('bookings.minutes')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(MoneyIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                color: \"text.secondary\",\n                children: t('bookings.price')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [\"$\", lessonPrice.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                color: \"text.secondary\",\n                children: t('bookings.status')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: getStatusText(selectedBooking.status),\n              color: getStatusColor(selectedBooking.status),\n              variant: \"filled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDetailsDialogOpen(false),\n          children: t('common.close')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), selectedBooking && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => canJoinMeeting(selectedBooking) && handleJoinMeeting(selectedBooking),\n          color: canJoinMeeting(selectedBooking) ? \"success\" : \"inherit\",\n          variant: canJoinMeeting(selectedBooking) ? \"contained\" : \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(VideoCallIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 26\n          }, this),\n          disabled: !canJoinMeeting(selectedBooking),\n          sx: {\n            mr: 1,\n            ...(canJoinMeeting(selectedBooking) ? {} : {\n              color: theme.palette.grey[500],\n              borderColor: theme.palette.grey[300],\n              backgroundColor: theme.palette.grey[100],\n              '&:hover': {\n                backgroundColor: theme.palette.grey[200]\n              }\n            })\n          },\n          children: canJoinMeeting(selectedBooking) ? t('meetings.join') : t('meetings.notStarted')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this), (selectedBooking === null || selectedBooking === void 0 ? void 0 : selectedBooking.status) === 'scheduled' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setDetailsDialogOpen(false);\n            setCancelDialogOpen(true);\n          },\n          color: \"error\",\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 26\n          }, this),\n          children: t('bookings.cancel')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Cancel confirmation dialog\n  const renderCancelDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: cancelDialogOpen,\n    onClose: () => setCancelDialogOpen(false),\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: [t('bookings.confirmCancel'), /*#__PURE__*/_jsxDEV(IconButton, {\n        \"aria-label\": \"close\",\n        onClick: () => setCancelDialogOpen(false),\n        sx: {\n          position: 'absolute',\n          right: 8,\n          top: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: t('bookings.cancelWarning')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this), selectedBooking && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [t('bookings.student'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 15\n          }, this), \" \", selectedBooking.student_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [t('bookings.date'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 15\n          }, this), \" \", formatBookingDate(selectedBooking.datetime)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [t('bookings.time'), \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 15\n          }, this), \" \", formatBookingTime(selectedBooking.datetime)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setCancelDialogOpen(false),\n        children: t('common.cancel')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCancelBooking,\n        color: \"error\",\n        variant: \"contained\",\n        disabled: cancellingBooking,\n        children: cancellingBooking ? t('bookings.cancelling') : t('bookings.confirmCancelButton')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 561,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        py: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 3,\n        sx: {\n          p: 3,\n          mb: 4,\n          bgcolor: 'primary.main',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            flexWrap: 'wrap',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              gutterBottom: true,\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: t('teacher.weeklyBookings')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                opacity: 0.9\n              },\n              children: t('teacher.weeklyBookingsDescription')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'right'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  opacity: 0.8,\n                  mb: 0.5\n                },\n                children: t('booking.weekNavigation')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: [\"\\uD83D\\uDCC5 \", format(currentWeekStart, 'MMM d', {\n                  locale: isRtl ? ar : enUS\n                }), \" - \", format(addDays(currentWeekStart, 6), 'MMM d, yyyy', {\n                  locale: isRtl ? ar : enUS\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: t('booking.previousWeek'),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: goToPreviousWeek,\n                    disabled: isPreviousWeekDisabled(),\n                    sx: {\n                      color: 'white',\n                      bgcolor: 'rgba(255, 255, 255, 0.1)',\n                      '&:hover': {\n                        bgcolor: 'rgba(255, 255, 255, 0.2)'\n                      },\n                      '&:disabled': {\n                        color: 'rgba(255, 255, 255, 0.3)',\n                        bgcolor: 'rgba(255, 255, 255, 0.05)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ChevronLeftIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 646,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: t('booking.nextWeek'),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: goToNextWeek,\n                    disabled: isNextWeekDisabled(),\n                    sx: {\n                      color: 'white',\n                      bgcolor: 'rgba(255, 255, 255, 0.1)',\n                      '&:hover': {\n                        bgcolor: 'rgba(255, 255, 255, 0.2)'\n                      },\n                      '&:disabled': {\n                        color: 'rgba(255, 255, 255, 0.3)',\n                        bgcolor: 'rgba(255, 255, 255, 0.05)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ChevronRightIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 2,\n        sx: {\n          p: 2,\n          mb: 3,\n          bgcolor: 'background.paper'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: 'primary.main',\n              color: 'white',\n              p: 1,\n              borderRadius: 1,\n              minWidth: 40,\n              textAlign: 'center'\n            },\n            children: \"\\uD83D\\uDD50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 0.5\n              },\n              children: t('bookings.currentTime')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: teacherProfile !== null && teacherProfile !== void 0 && teacherProfile.timezone ? moment().utc().add(parseTimezoneOffset(teacherProfile.timezone), 'minutes').format('dddd, MMMM D, YYYY [at] h:mm A') : format(new Date(), 'PPpp', {\n                locale: isRtl ? ar : enUS\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this), (teacherProfile === null || teacherProfile === void 0 ? void 0 : teacherProfile.timezone) && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                opacity: 0.8\n              },\n              children: teacherProfile.timezone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          py: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 4\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(WeeklyBookingsTable, {\n        bookings: bookings,\n        loading: loading,\n        currentWeekStart: currentWeekStart,\n        daysOfWeek: daysOfWeek,\n        onViewDetails: handleViewDetails,\n        onCancelBooking: handleCancelBookingClick // Now teachers can cancel bookings\n        ,\n        studentProfile: teacherProfile // Pass teacher profile for timezone conversion\n        ,\n        formatBookingTime: formatBookingTime,\n        getStatusColor: getStatusColor,\n        getStatusText: getStatusText,\n        isTeacherView: true // Add this prop to distinguish teacher view\n        ,\n        availableHours: availableHours // Pass available hours to show available slots\n        ,\n        onTakeBreak: handleTakeBreak // Pass take break handler\n        ,\n        weeklyBreaks: weeklyBreaks // Pass weekly breaks\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), renderCancelDialog()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      fullScreen: true,\n      open: openMeeting,\n      onClose: handleCloseMeeting,\n      children: currentMeeting && /*#__PURE__*/_jsxDEV(VideoSDKMeeting, {\n        roomId: currentMeeting.room_name,\n        meetingId: currentMeeting.meeting_id,\n        meetingData: currentMeeting,\n        onClose: handleCloseMeeting\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 743,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 607,\n    columnNumber: 5\n  }, this);\n};\n_s(TeacherBookings, \"uYr3fAb6PgPQTOoDHh9byNH8Slc=\", false, function () {\n  return [useTranslation, useAuth, useTheme];\n});\n_c = TeacherBookings;\nexport default TeacherBookings;\nvar _c;\n$RefreshReg$(_c, \"TeacherBookings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Container", "Typography", "Box", "Paper", "CircularProgress", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Avatar", "Chip", "Grid", "useTheme", "IconButton", "<PERSON><PERSON><PERSON>", "CalendarToday", "CalendarIcon", "AccessTime", "TimeIcon", "Person", "PersonIcon", "AttachMoney", "MoneyIcon", "ChevronLeft", "ChevronLeftIcon", "ChevronRight", "ChevronRightIcon", "VideoCall", "VideoCallIcon", "Cancel", "CancelIcon", "Close", "CloseIcon", "axios", "format", "startOfWeek", "addDays", "addWeeks", "subWeeks", "ar", "enUS", "useAuth", "Layout", "WeeklyBookingsTable", "convertFromDatabaseTime", "formatDateInStudentTimezone", "getCurrentTimeInTimezone", "parseTimezoneOffset", "moment", "VideoSDKMeeting", "jsxDEV", "_jsxDEV", "TeacherBookings", "_s", "t", "i18n", "token", "theme", "isRtl", "language", "bookings", "setBookings", "loading", "setLoading", "error", "setError", "selectedBooking", "setSelectedBooking", "detailsDialogOpen", "setDetailsDialogOpen", "teacher<PERSON><PERSON><PERSON><PERSON>", "setTeacherProfile", "cancelDialogOpen", "setCancelDialogOpen", "cancellingBooking", "setCancellingBooking", "openMeeting", "setOpenMeeting", "currentMeeting", "setCurrentMeeting", "currentTime", "setCurrentTime", "Date", "availableHours", "setAvailableHours", "weeklyBreaks", "setWeeklyBreaks", "currentWeekStart", "setCurrentWeekStart", "today", "weekStartsOn", "daysOfWeek", "goToPreviousWeek", "previousWeek", "goToNextWeek", "nextWeek", "oneYearAhead", "maxWeek", "isPreviousWeekDisabled", "isNextWeekDisabled", "timeInterval", "setInterval", "clearInterval", "fetchBookings", "data", "get", "headers", "success", "console", "log", "processedBookings", "map", "booking", "price_per_lesson", "parseFloat", "duration", "String", "teacherTimezone", "timezone", "fetchAvailableHours", "fetchWeeklyBreaks", "message", "_error$response", "_error$response$data", "response", "profile", "available_hours", "parsedHours", "JSON", "parse", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "weekStart", "handleTakeBreak", "breakSlot", "breakDate", "date", "breakDateStr", "weekStartStr", "breakData", "hour", "minute", "week_start", "post", "prev", "toString", "alert", "handleViewDetails", "handleCancelBookingClick", "handleJoinMeeting", "room_name", "meeting_id", "datetime", "handleCloseMeeting", "getMeetingStatus", "status", "canJoinMeeting", "currentStatus", "meetingStartTime", "meetingEndTime", "setMinutes", "getMinutes", "parseInt", "now", "handleCancelBooking", "put", "id", "prevBookings", "_error$response2", "_error$response2$data", "getStatusColor", "getStatusText", "defaultValue", "char<PERSON>t", "toUpperCase", "slice", "formatBookingDate", "locale", "formattedDate", "formatBookingTime", "formattedDateTime", "calculateLessonPrice", "pricePer<PERSON><PERSON>on", "durationNum", "renderDetailsDialog", "lessonPrice", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "sx", "bgcolor", "color", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "mt", "container", "spacing", "item", "xs", "mb", "src", "student_picture", "alt", "student_name", "mr", "width", "height", "student_email", "sm", "toFixed", "label", "onClick", "startIcon", "disabled", "palette", "grey", "borderColor", "backgroundColor", "renderCancelDialog", "position", "right", "top", "gutterBottom", "py", "elevation", "p", "justifyContent", "flexWrap", "fontWeight", "opacity", "textAlign", "title", "borderRadius", "min<PERSON><PERSON><PERSON>", "utc", "add", "severity", "onViewDetails", "onCancelBooking", "studentProfile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onTakeBreak", "fullScreen", "roomId", "meetingId", "meetingData", "_c", "$RefreshReg$"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/teacher/Bookings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Container,\n  Typography,\n  Box,\n  Paper,\n  CircularProgress,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Avatar,\n  Chip,\n  Grid,\n  useTheme,\n  IconButton,\n  Tooltip\n} from '@mui/material';\nimport {\n  CalendarToday as CalendarIcon,\n  AccessTime as TimeIcon,\n  Person as PersonIcon,\n  AttachMoney as MoneyIcon,\n  ChevronLeft as ChevronLeftIcon,\n  ChevronRight as ChevronRightIcon,\n  VideoCall as VideoCallIcon,\n  Cancel as CancelIcon,\n  Close as CloseIcon\n} from '@mui/icons-material';\nimport axios from 'axios';\nimport { format, startOfWeek, addDays, addWeeks, subWeeks } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Layout from '../../components/Layout';\nimport WeeklyBookingsTable from '../../components/WeeklyBookingsTable';\nimport { convertFromDatabaseTime, formatDateInStudentTimezone, getCurrentTimeInTimezone, parseTimezoneOffset } from '../../utils/timezone';\nimport moment from 'moment-timezone';\nimport VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';\n\nconst TeacherBookings = () => {\n  const { t, i18n } = useTranslation();\n  const { token } = useAuth();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n\n  const [bookings, setBookings] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedBooking, setSelectedBooking] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n  const [teacherProfile, setTeacherProfile] = useState(null);\n  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);\n  const [cancellingBooking, setCancellingBooking] = useState(false);\n  const [openMeeting, setOpenMeeting] = useState(false);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [availableHours, setAvailableHours] = useState(null);\n  const [weeklyBreaks, setWeeklyBreaks] = useState([]);\n\n  // Week navigation\n  const [currentWeekStart, setCurrentWeekStart] = useState(() => {\n    const today = new Date();\n    return startOfWeek(today, { weekStartsOn: 1 }); // Start from current week\n  });\n\n  // Days of the week (format expected by WeeklyBookingsTable)\n  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n\n  // Week navigation functions\n  const goToPreviousWeek = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    setCurrentWeekStart(previousWeek);\n  };\n\n  const goToNextWeek = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n\n    // Don't allow going beyond one year ahead\n    if (nextWeek <= maxWeek) {\n      setCurrentWeekStart(nextWeek);\n    }\n  };\n\n  // Check if navigation buttons should be disabled\n  const isPreviousWeekDisabled = () => false;\n\n  const isNextWeekDisabled = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n    return nextWeek > maxWeek;\n  };\n\n  // Update current time every second\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Fetch bookings\n  useEffect(() => {\n    const fetchBookings = async () => {\n      try {\n        setLoading(true);\n        const { data } = await axios.get('/api/bookings/teacher', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success) {\n          console.log('Teacher bookings data:', data.data);\n\n          // Make sure all bookings have the correct data types\n          const processedBookings = data.data.map(booking => ({\n            ...booking,\n            price_per_lesson: parseFloat(booking.price_per_lesson || 0),\n            duration: booking.duration ? String(booking.duration) : '50'\n          }));\n\n          console.log('Processed teacher bookings:', processedBookings);\n          setBookings(processedBookings);\n\n          // Set teacher profile with timezone information\n          if (data.teacherTimezone) {\n            setTeacherProfile({ timezone: data.teacherTimezone });\n          }\n\n          // Fetch teacher's available hours\n          fetchAvailableHours();\n\n          // Fetch weekly breaks\n          fetchWeeklyBreaks();\n        } else {\n          setError(data.message || t('bookings.fetchError'));\n        }\n      } catch (error) {\n        console.error('Error fetching teacher bookings:', error);\n        setError(error.response?.data?.message || t('bookings.fetchError'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (token) {\n      fetchBookings();\n    }\n  }, [token, t]);\n\n  // Fetch weekly breaks when week changes\n  useEffect(() => {\n    if (token) {\n      fetchWeeklyBreaks();\n    }\n  }, [currentWeekStart, token]);\n\n  // Fetch available hours\n  const fetchAvailableHours = async () => {\n    try {\n      const { data } = await axios.get('/api/teacher/profile', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success && data.profile.available_hours) {\n        const parsedHours = typeof data.profile.available_hours === 'string'\n          ? JSON.parse(data.profile.available_hours)\n          : data.profile.available_hours;\n        console.log('Available hours loaded:', parsedHours);\n        setAvailableHours(parsedHours);\n      }\n    } catch (error) {\n      console.error('Error fetching available hours:', error);\n    }\n  };\n\n  // Fetch weekly breaks\n  const fetchWeeklyBreaks = async () => {\n    try {\n      // Use teacher's timezone instead of UTC\n      const year = currentWeekStart.getFullYear();\n      const month = String(currentWeekStart.getMonth() + 1).padStart(2, '0');\n      const day = String(currentWeekStart.getDate()).padStart(2, '0');\n      const weekStart = `${year}-${month}-${day}`;\n\n      const { data } = await axios.get(`/api/teacher/weekly-breaks?week_start=${weekStart}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        console.log('🔍 FRONTEND: Weekly breaks received from server:', data.data);\n        console.log('🔍 FRONTEND: Setting weeklyBreaks state to:', data.data);\n        setWeeklyBreaks(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching weekly breaks:', error);\n    }\n  };\n\n  // Handle take break\n  const handleTakeBreak = async (breakSlot) => {\n    try {\n      // Use teacher's timezone for both date and week_start\n      const breakDate = breakSlot.date;\n      const breakDateStr = `${breakDate.getFullYear()}-${String(breakDate.getMonth() + 1).padStart(2, '0')}-${String(breakDate.getDate()).padStart(2, '0')}`;\n\n      const weekStartStr = `${currentWeekStart.getFullYear()}-${String(currentWeekStart.getMonth() + 1).padStart(2, '0')}-${String(currentWeekStart.getDate()).padStart(2, '0')}`;\n\n      const breakData = {\n        date: breakDateStr,\n        hour: breakSlot.hour,\n        minute: breakSlot.minute,\n        week_start: weekStartStr\n      };\n\n      const { data } = await axios.post('/api/teacher/take-break', breakData, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        // Add to weekly breaks list\n        setWeeklyBreaks(prev => [...prev, `${breakData.date}_${breakData.hour}:${breakData.minute.toString().padStart(2, '0')}`]);\n\n        // Show success message\n        alert(t('bookings.breakTakenSuccess', 'تم أخذ الراحة بنجاح'));\n      }\n    } catch (error) {\n      console.error('Error taking break:', error);\n      alert(t('bookings.breakTakenError', 'خطأ في أخذ الراحة'));\n    }\n  };\n\n  // Handle view details\n  const handleViewDetails = (booking) => {\n    setSelectedBooking(booking);\n    setDetailsDialogOpen(true);\n  };\n\n  // Handle cancel booking\n  const handleCancelBookingClick = (booking) => {\n    setSelectedBooking(booking);\n    setCancelDialogOpen(true);\n  };\n\n  // Handle join meeting\n  const handleJoinMeeting = async (booking) => {\n    try {\n      // Check if room_name exists from the booking data\n      if (!booking.room_name) {\n        console.error('No room_name found for booking:', booking);\n        alert(t('meetings.noRoomError') || 'Meeting room not found');\n        return;\n      }\n\n      // Check if meeting_id exists\n      if (!booking.meeting_id) {\n        console.error('No meeting_id found for booking:', booking);\n        alert(t('meetings.noMeetingError') || 'Meeting ID not found');\n        return;\n      }\n\n      console.log('Joining meeting with data:', {\n        room_name: booking.room_name,\n        meeting_id: booking.meeting_id,\n        datetime: booking.datetime,\n        duration: booking.duration\n      });\n\n      // Validate room\n      const response = await axios.get(`/meetings/${booking.room_name}/validate`);\n      setCurrentMeeting({ ...booking, room_name: booking.room_name });\n      setOpenMeeting(true);\n    } catch (error) {\n      console.error('Error joining meeting:', error);\n      alert(t('meetings.joinError'));\n    }\n  };\n\n  const handleCloseMeeting = () => {\n    setOpenMeeting(false);\n    setCurrentMeeting(null);\n  };\n\n  // Get meeting status from database directly\n  const getMeetingStatus = (booking) => {\n    return booking.status || 'scheduled';\n  };\n\n  // Check if user can join meeting\n  const canJoinMeeting = (booking) => {\n    if (!booking || !teacherProfile) return false;\n\n    const currentStatus = getMeetingStatus(booking);\n    if (currentStatus === 'cancelled' || currentStatus === 'completed') {\n      return false;\n    }\n\n    const meetingStartTime = new Date(booking.datetime);\n    const meetingEndTime = new Date(booking.datetime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + parseInt(booking.duration));\n    const now = new Date();\n\n    return now >= meetingStartTime && now < meetingEndTime;\n  };\n\n  // Handle booking cancellation\n  const handleCancelBooking = async () => {\n    if (!selectedBooking) return;\n\n    try {\n      setCancellingBooking(true);\n      const { data } = await axios.put(`/bookings/${selectedBooking.id}/cancel`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        // Update the booking status in the local state\n        setBookings(prevBookings =>\n          prevBookings.map(booking =>\n            booking.id === selectedBooking.id\n              ? { ...booking, status: 'cancelled' }\n              : booking\n          )\n        );\n        alert(t('bookings.cancelSuccess'));\n      } else {\n        alert(data.message || t('bookings.cancelError'));\n      }\n    } catch (error) {\n      console.error('Error cancelling booking:', error);\n      alert(error.response?.data?.message || t('bookings.cancelError'));\n    } finally {\n      setCancellingBooking(false);\n      setCancelDialogOpen(false);\n      setDetailsDialogOpen(false);\n      setSelectedBooking(null);\n    }\n  };\n\n  // Get status chip color\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'scheduled':\n        return 'primary';\n      case 'completed':\n        return 'success';\n      case 'cancelled':\n        return 'error';\n      case 'issue_reported':\n        return 'warning';\n      case 'ongoing':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n\n  // Get translated status text\n  const getStatusText = (status) => {\n    return t(`bookings.statusValues.${status}`, { \n      defaultValue: status.charAt(0).toUpperCase() + status.slice(1) \n    });\n  };\n\n  // Format booking date in teacher's timezone\n  const formatBookingDate = (datetime) => {\n    if (!teacherProfile || !teacherProfile.timezone) {\n      return format(new Date(datetime), 'PPP', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDate = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD');\n    return moment(formattedDate, 'YYYY-MM-DD').format('MMMM D, YYYY');\n  };\n\n  // Format booking time in teacher's timezone\n  const formatBookingTime = (datetime) => {\n    if (!teacherProfile || !teacherProfile.timezone) {\n      return format(new Date(datetime), 'p', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDateTime = formatDateInStudentTimezone(datetime, teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    return moment(formattedDateTime, 'YYYY-MM-DD HH:mm:ss').format('h:mm A');\n  };\n\n  // Calculate lesson price based on duration\n  const calculateLessonPrice = (pricePerLesson, duration) => {\n    const durationNum = parseInt(duration, 10);\n    return durationNum === 50 ? pricePerLesson : pricePerLesson / 2;\n  };\n\n  // Render details dialog\n  const renderDetailsDialog = () => {\n    if (!selectedBooking) return null;\n\n    const lessonPrice = calculateLessonPrice(selectedBooking.price_per_lesson, selectedBooking.duration);\n\n    return (\n      <Dialog\n        open={detailsDialogOpen}\n        onClose={() => setDetailsDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <CalendarIcon />\n            <Typography variant=\"h6\">\n              {t('bookings.bookingDetails')}\n            </Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent sx={{ mt: 2 }}>\n          <Grid container spacing={3}>\n            <Grid item xs={12}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <Avatar\n                  src={selectedBooking.student_picture}\n                  alt={selectedBooking.student_name}\n                  sx={{ mr: 2, width: 56, height: 56 }}\n                />\n                <Box>\n                  <Typography variant=\"h6\">\n                    {selectedBooking.student_name}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {selectedBooking.student_email}\n                  </Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <CalendarIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.date')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                {formatBookingDate(selectedBooking.datetime)}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.time')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                {formatBookingTime(selectedBooking.datetime)}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.duration')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                {selectedBooking.duration} {t('bookings.minutes')}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} sm={6}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <MoneyIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.price')}\n                </Typography>\n              </Box>\n              <Typography variant=\"body1\">\n                ${lessonPrice.toFixed(2)}\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  {t('bookings.status')}\n                </Typography>\n              </Box>\n              <Chip\n                label={getStatusText(selectedBooking.status)}\n                color={getStatusColor(selectedBooking.status)}\n                variant=\"filled\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDetailsDialogOpen(false)}>\n            {t('common.close')}\n          </Button>\n\n          {/* Join Meeting Button */}\n          {selectedBooking && (\n            <Button\n              onClick={() => canJoinMeeting(selectedBooking) && handleJoinMeeting(selectedBooking)}\n              color={canJoinMeeting(selectedBooking) ? \"success\" : \"inherit\"}\n              variant={canJoinMeeting(selectedBooking) ? \"contained\" : \"outlined\"}\n              startIcon={<VideoCallIcon />}\n              disabled={!canJoinMeeting(selectedBooking)}\n              sx={{\n                mr: 1,\n                ...(canJoinMeeting(selectedBooking) ? {} : {\n                  color: theme.palette.grey[500],\n                  borderColor: theme.palette.grey[300],\n                  backgroundColor: theme.palette.grey[100],\n                  '&:hover': {\n                    backgroundColor: theme.palette.grey[200],\n                  }\n                })\n              }}\n            >\n              {canJoinMeeting(selectedBooking) ? t('meetings.join') : t('meetings.notStarted')}\n            </Button>\n          )}\n\n          {selectedBooking?.status === 'scheduled' && (\n            <Button\n              onClick={() => {\n                setDetailsDialogOpen(false);\n                setCancelDialogOpen(true);\n              }}\n              color=\"error\"\n              variant=\"contained\"\n              startIcon={<CancelIcon />}\n            >\n              {t('bookings.cancel')}\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Cancel confirmation dialog\n  const renderCancelDialog = () => (\n    <Dialog open={cancelDialogOpen} onClose={() => setCancelDialogOpen(false)}>\n      <DialogTitle>\n        {t('bookings.confirmCancel')}\n        <IconButton\n          aria-label=\"close\"\n          onClick={() => setCancelDialogOpen(false)}\n          sx={{ position: 'absolute', right: 8, top: 8 }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n      <DialogContent>\n        <Typography variant=\"body1\">\n          {t('bookings.cancelWarning')}\n        </Typography>\n        {selectedBooking && (\n          <Box sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.student')}:</strong> {selectedBooking.student_name}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.date')}:</strong> {formatBookingDate(selectedBooking.datetime)}\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>{t('bookings.time')}:</strong> {formatBookingTime(selectedBooking.datetime)}\n            </Typography>\n          </Box>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={() => setCancelDialogOpen(false)}>\n          {t('common.cancel')}\n        </Button>\n        <Button\n          onClick={handleCancelBooking}\n          color=\"error\"\n          variant=\"contained\"\n          disabled={cancellingBooking}\n        >\n          {cancellingBooking ? t('bookings.cancelling') : t('bookings.confirmCancelButton')}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Layout>\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        <Paper elevation={3} sx={{ p: 3, mb: 4, bgcolor: 'primary.main', color: 'white' }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>\n            <Box>\n              <Typography variant=\"h4\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                {t('teacher.weeklyBookings')}\n              </Typography>\n              <Typography variant=\"body1\" sx={{ opacity: 0.9 }}>\n                {t('teacher.weeklyBookingsDescription')}\n              </Typography>\n            </Box>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <Box sx={{ textAlign: 'right' }}>\n                <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 0.5 }}>\n                  {t('booking.weekNavigation')}\n                </Typography>\n                <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                  📅 {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                </Typography>\n              </Box>\n              <Box sx={{ display: 'flex', gap: 1 }}>\n                <Tooltip title={t('booking.previousWeek')}>\n                  <span>\n                    <IconButton\n                      onClick={goToPreviousWeek}\n                      disabled={isPreviousWeekDisabled()}\n                      sx={{\n                        color: 'white',\n                        bgcolor: 'rgba(255, 255, 255, 0.1)',\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 255, 255, 0.2)',\n                        },\n                        '&:disabled': {\n                          color: 'rgba(255, 255, 255, 0.3)',\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        }\n                      }}\n                    >\n                      <ChevronLeftIcon />\n                    </IconButton>\n                  </span>\n                </Tooltip>\n                <Tooltip title={t('booking.nextWeek')}>\n                  <span>\n                    <IconButton\n                      onClick={goToNextWeek}\n                      disabled={isNextWeekDisabled()}\n                      sx={{\n                        color: 'white',\n                        bgcolor: 'rgba(255, 255, 255, 0.1)',\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 255, 255, 0.2)',\n                        },\n                        '&:disabled': {\n                          color: 'rgba(255, 255, 255, 0.3)',\n                          bgcolor: 'rgba(255, 255, 255, 0.05)',\n                        }\n                      }}\n                    >\n                      <ChevronRightIcon />\n                    </IconButton>\n                  </span>\n                </Tooltip>\n              </Box>\n            </Box>\n          </Box>\n        </Paper>\n\n        {/* Current Time Display */}\n        <Paper elevation={2} sx={{ p: 2, mb: 3, bgcolor: 'background.paper' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <Box sx={{\n              bgcolor: 'primary.main',\n              color: 'white',\n              p: 1,\n              borderRadius: 1,\n              minWidth: 40,\n              textAlign: 'center'\n            }}>\n              🕐\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 0.5 }}>\n                {t('bookings.currentTime')}\n              </Typography>\n              <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                {teacherProfile?.timezone ? (\n                  moment().utc().add(parseTimezoneOffset(teacherProfile.timezone), 'minutes').format('dddd, MMMM D, YYYY [at] h:mm A')\n                ) : (\n                  format(new Date(), 'PPpp', {\n                    locale: isRtl ? ar : enUS\n                  })\n                )}\n              </Typography>\n              {teacherProfile?.timezone && (\n                <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                  {teacherProfile.timezone}\n                </Typography>\n              )}\n            </Box>\n          </Box>\n        </Paper>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>\n            <CircularProgress />\n          </Box>\n        ) : error ? (\n          <Alert severity=\"error\" sx={{ mb: 4 }}>\n            {error}\n          </Alert>\n        ) : (\n          <WeeklyBookingsTable\n            bookings={bookings}\n            loading={loading}\n            currentWeekStart={currentWeekStart}\n            daysOfWeek={daysOfWeek}\n            onViewDetails={handleViewDetails}\n            onCancelBooking={handleCancelBookingClick} // Now teachers can cancel bookings\n            studentProfile={teacherProfile} // Pass teacher profile for timezone conversion\n            formatBookingTime={formatBookingTime}\n            getStatusColor={getStatusColor}\n            getStatusText={getStatusText}\n            isTeacherView={true} // Add this prop to distinguish teacher view\n            availableHours={availableHours} // Pass available hours to show available slots\n            onTakeBreak={handleTakeBreak} // Pass take break handler\n            weeklyBreaks={weeklyBreaks} // Pass weekly breaks\n          />\n        )}\n\n        {renderDetailsDialog()}\n        {renderCancelDialog()}\n      </Container>\n\n      {/* Meeting Dialog */}\n      <Dialog\n        fullScreen\n        open={openMeeting}\n        onClose={handleCloseMeeting}\n      >\n        {currentMeeting && (\n          <VideoSDKMeeting\n            roomId={currentMeeting.room_name}\n            meetingId={currentMeeting.meeting_id}\n            meetingData={currentMeeting}\n            onClose={handleCloseMeeting}\n          />\n        )}\n      </Dialog>\n    </Layout>\n  );\n};\n\nexport default TeacherBookings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,SACEC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,aAAa,IAAIC,YAAY,EAC7BC,UAAU,IAAIC,QAAQ,EACtBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,SAAS,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,YAAY,IAAIC,gBAAgB,EAChCC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,UAAU;AAC3E,SAASC,EAAE,EAAEC,IAAI,QAAQ,iBAAiB;AAC1C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,SAASC,uBAAuB,EAAEC,2BAA2B,EAAEC,wBAAwB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAC1I,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,eAAe,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAG1D,cAAc,CAAC,CAAC;EACpC,MAAM;IAAE2D;EAAM,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3B,MAAMgB,KAAK,GAAG7C,QAAQ,CAAC,CAAC;EACxB,MAAM8C,KAAK,GAAGH,IAAI,CAACI,QAAQ,KAAK,IAAI;EAEpC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmE,OAAO,EAAEC,UAAU,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqE,KAAK,EAAEC,QAAQ,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2E,cAAc,EAAEC,iBAAiB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACiF,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmF,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,IAAIuF,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0F,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,MAAM;IAC7D,MAAM8F,KAAK,GAAG,IAAIP,IAAI,CAAC,CAAC;IACxB,OAAO/C,WAAW,CAACsD,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;;EAEjG;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,YAAY,GAAGvD,QAAQ,CAACiD,gBAAgB,EAAE,CAAC,CAAC;IAClDC,mBAAmB,CAACK,YAAY,CAAC;EACnC,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,QAAQ,GAAG1D,QAAQ,CAACkD,gBAAgB,EAAE,CAAC,CAAC;IAC9C,MAAME,KAAK,GAAG,IAAIP,IAAI,CAAC,CAAC;IACxB,MAAMc,YAAY,GAAG3D,QAAQ,CAACoD,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;IAC1C,MAAMQ,OAAO,GAAG9D,WAAW,CAAC6D,YAAY,EAAE;MAAEN,YAAY,EAAE;IAAE,CAAC,CAAC;;IAE9D;IACA,IAAIK,QAAQ,IAAIE,OAAO,EAAE;MACvBT,mBAAmB,CAACO,QAAQ,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMG,sBAAsB,GAAGA,CAAA,KAAM,KAAK;EAE1C,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMJ,QAAQ,GAAG1D,QAAQ,CAACkD,gBAAgB,EAAE,CAAC,CAAC;IAC9C,MAAME,KAAK,GAAG,IAAIP,IAAI,CAAC,CAAC;IACxB,MAAMc,YAAY,GAAG3D,QAAQ,CAACoD,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;IAC1C,MAAMQ,OAAO,GAAG9D,WAAW,CAAC6D,YAAY,EAAE;MAAEN,YAAY,EAAE;IAAE,CAAC,CAAC;IAC9D,OAAOK,QAAQ,GAAGE,OAAO;EAC3B,CAAC;;EAED;EACArG,SAAS,CAAC,MAAM;IACd,MAAMwG,YAAY,GAAGC,WAAW,CAAC,MAAM;MACrCpB,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMoB,aAAa,CAACF,YAAY,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxG,SAAS,CAAC,MAAM;IACd,MAAM2G,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFxC,UAAU,CAAC,IAAI,CAAC;QAChB,MAAM;UAAEyC;QAAK,CAAC,GAAG,MAAMvE,KAAK,CAACwE,GAAG,CAAC,uBAAuB,EAAE;UACxDC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUlD,KAAK;UAClC;QACF,CAAC,CAAC;QAEF,IAAIgD,IAAI,CAACG,OAAO,EAAE;UAChBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEL,IAAI,CAACA,IAAI,CAAC;;UAEhD;UACA,MAAMM,iBAAiB,GAAGN,IAAI,CAACA,IAAI,CAACO,GAAG,CAACC,OAAO,KAAK;YAClD,GAAGA,OAAO;YACVC,gBAAgB,EAAEC,UAAU,CAACF,OAAO,CAACC,gBAAgB,IAAI,CAAC,CAAC;YAC3DE,QAAQ,EAAEH,OAAO,CAACG,QAAQ,GAAGC,MAAM,CAACJ,OAAO,CAACG,QAAQ,CAAC,GAAG;UAC1D,CAAC,CAAC,CAAC;UAEHP,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,iBAAiB,CAAC;UAC7DjD,WAAW,CAACiD,iBAAiB,CAAC;;UAE9B;UACA,IAAIN,IAAI,CAACa,eAAe,EAAE;YACxB9C,iBAAiB,CAAC;cAAE+C,QAAQ,EAAEd,IAAI,CAACa;YAAgB,CAAC,CAAC;UACvD;;UAEA;UACAE,mBAAmB,CAAC,CAAC;;UAErB;UACAC,iBAAiB,CAAC,CAAC;QACrB,CAAC,MAAM;UACLvD,QAAQ,CAACuC,IAAI,CAACiB,OAAO,IAAInE,CAAC,CAAC,qBAAqB,CAAC,CAAC;QACpD;MACF,CAAC,CAAC,OAAOU,KAAK,EAAE;QAAA,IAAA0D,eAAA,EAAAC,oBAAA;QACdf,OAAO,CAAC5C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDC,QAAQ,CAAC,EAAAyD,eAAA,GAAA1D,KAAK,CAAC4D,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBlB,IAAI,cAAAmB,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAInE,CAAC,CAAC,qBAAqB,CAAC,CAAC;MACrE,CAAC,SAAS;QACRS,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIP,KAAK,EAAE;MACT+C,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAC/C,KAAK,EAAEF,CAAC,CAAC,CAAC;;EAEd;EACA1D,SAAS,CAAC,MAAM;IACd,IAAI4D,KAAK,EAAE;MACTgE,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACjC,gBAAgB,EAAE/B,KAAK,CAAC,CAAC;;EAE7B;EACA,MAAM+D,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAM;QAAEf;MAAK,CAAC,GAAG,MAAMvE,KAAK,CAACwE,GAAG,CAAC,sBAAsB,EAAE;QACvDC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUlD,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAIgD,IAAI,CAACG,OAAO,IAAIH,IAAI,CAACqB,OAAO,CAACC,eAAe,EAAE;QAChD,MAAMC,WAAW,GAAG,OAAOvB,IAAI,CAACqB,OAAO,CAACC,eAAe,KAAK,QAAQ,GAChEE,IAAI,CAACC,KAAK,CAACzB,IAAI,CAACqB,OAAO,CAACC,eAAe,CAAC,GACxCtB,IAAI,CAACqB,OAAO,CAACC,eAAe;QAChClB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkB,WAAW,CAAC;QACnD3C,iBAAiB,CAAC2C,WAAW,CAAC;MAChC;IACF,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACd4C,OAAO,CAAC5C,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMwD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF;MACA,MAAMU,IAAI,GAAG3C,gBAAgB,CAAC4C,WAAW,CAAC,CAAC;MAC3C,MAAMC,KAAK,GAAGhB,MAAM,CAAC7B,gBAAgB,CAAC8C,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtE,MAAMC,GAAG,GAAGnB,MAAM,CAAC7B,gBAAgB,CAACiD,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC/D,MAAMG,SAAS,GAAG,GAAGP,IAAI,IAAIE,KAAK,IAAIG,GAAG,EAAE;MAE3C,MAAM;QAAE/B;MAAK,CAAC,GAAG,MAAMvE,KAAK,CAACwE,GAAG,CAAC,yCAAyCgC,SAAS,EAAE,EAAE;QACrF/B,OAAO,EAAE;UACP,eAAe,EAAE,UAAUlD,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAIgD,IAAI,CAACG,OAAO,EAAE;QAChBC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEL,IAAI,CAACA,IAAI,CAAC;QAC1EI,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEL,IAAI,CAACA,IAAI,CAAC;QACrElB,eAAe,CAACkB,IAAI,CAACA,IAAI,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACd4C,OAAO,CAAC5C,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAM0E,eAAe,GAAG,MAAOC,SAAS,IAAK;IAC3C,IAAI;MACF;MACA,MAAMC,SAAS,GAAGD,SAAS,CAACE,IAAI;MAChC,MAAMC,YAAY,GAAG,GAAGF,SAAS,CAACT,WAAW,CAAC,CAAC,IAAIf,MAAM,CAACwB,SAAS,CAACP,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIlB,MAAM,CAACwB,SAAS,CAACJ,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MAEtJ,MAAMS,YAAY,GAAG,GAAGxD,gBAAgB,CAAC4C,WAAW,CAAC,CAAC,IAAIf,MAAM,CAAC7B,gBAAgB,CAAC8C,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIlB,MAAM,CAAC7B,gBAAgB,CAACiD,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MAE3K,MAAMU,SAAS,GAAG;QAChBH,IAAI,EAAEC,YAAY;QAClBG,IAAI,EAAEN,SAAS,CAACM,IAAI;QACpBC,MAAM,EAAEP,SAAS,CAACO,MAAM;QACxBC,UAAU,EAAEJ;MACd,CAAC;MAED,MAAM;QAAEvC;MAAK,CAAC,GAAG,MAAMvE,KAAK,CAACmH,IAAI,CAAC,yBAAyB,EAAEJ,SAAS,EAAE;QACtEtC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUlD,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAIgD,IAAI,CAACG,OAAO,EAAE;QAChB;QACArB,eAAe,CAAC+D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGL,SAAS,CAACH,IAAI,IAAIG,SAAS,CAACC,IAAI,IAAID,SAAS,CAACE,MAAM,CAACI,QAAQ,CAAC,CAAC,CAAChB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;;QAEzH;QACAiB,KAAK,CAACjG,CAAC,CAAC,4BAA4B,EAAE,qBAAqB,CAAC,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACd4C,OAAO,CAAC5C,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CuF,KAAK,CAACjG,CAAC,CAAC,0BAA0B,EAAE,mBAAmB,CAAC,CAAC;IAC3D;EACF,CAAC;;EAED;EACA,MAAMkG,iBAAiB,GAAIxC,OAAO,IAAK;IACrC7C,kBAAkB,CAAC6C,OAAO,CAAC;IAC3B3C,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMoF,wBAAwB,GAAIzC,OAAO,IAAK;IAC5C7C,kBAAkB,CAAC6C,OAAO,CAAC;IAC3BvC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMiF,iBAAiB,GAAG,MAAO1C,OAAO,IAAK;IAC3C,IAAI;MACF;MACA,IAAI,CAACA,OAAO,CAAC2C,SAAS,EAAE;QACtB/C,OAAO,CAAC5C,KAAK,CAAC,iCAAiC,EAAEgD,OAAO,CAAC;QACzDuC,KAAK,CAACjG,CAAC,CAAC,sBAAsB,CAAC,IAAI,wBAAwB,CAAC;QAC5D;MACF;;MAEA;MACA,IAAI,CAAC0D,OAAO,CAAC4C,UAAU,EAAE;QACvBhD,OAAO,CAAC5C,KAAK,CAAC,kCAAkC,EAAEgD,OAAO,CAAC;QAC1DuC,KAAK,CAACjG,CAAC,CAAC,yBAAyB,CAAC,IAAI,sBAAsB,CAAC;QAC7D;MACF;MAEAsD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QACxC8C,SAAS,EAAE3C,OAAO,CAAC2C,SAAS;QAC5BC,UAAU,EAAE5C,OAAO,CAAC4C,UAAU;QAC9BC,QAAQ,EAAE7C,OAAO,CAAC6C,QAAQ;QAC1B1C,QAAQ,EAAEH,OAAO,CAACG;MACpB,CAAC,CAAC;;MAEF;MACA,MAAMS,QAAQ,GAAG,MAAM3F,KAAK,CAACwE,GAAG,CAAC,aAAaO,OAAO,CAAC2C,SAAS,WAAW,CAAC;MAC3E5E,iBAAiB,CAAC;QAAE,GAAGiC,OAAO;QAAE2C,SAAS,EAAE3C,OAAO,CAAC2C;MAAU,CAAC,CAAC;MAC/D9E,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACd4C,OAAO,CAAC5C,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CuF,KAAK,CAACjG,CAAC,CAAC,oBAAoB,CAAC,CAAC;IAChC;EACF,CAAC;EAED,MAAMwG,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjF,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMgF,gBAAgB,GAAI/C,OAAO,IAAK;IACpC,OAAOA,OAAO,CAACgD,MAAM,IAAI,WAAW;EACtC,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIjD,OAAO,IAAK;IAClC,IAAI,CAACA,OAAO,IAAI,CAAC1C,cAAc,EAAE,OAAO,KAAK;IAE7C,MAAM4F,aAAa,GAAGH,gBAAgB,CAAC/C,OAAO,CAAC;IAC/C,IAAIkD,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,WAAW,EAAE;MAClE,OAAO,KAAK;IACd;IAEA,MAAMC,gBAAgB,GAAG,IAAIjF,IAAI,CAAC8B,OAAO,CAAC6C,QAAQ,CAAC;IACnD,MAAMO,cAAc,GAAG,IAAIlF,IAAI,CAAC8B,OAAO,CAAC6C,QAAQ,CAAC;IACjDO,cAAc,CAACC,UAAU,CAACD,cAAc,CAACE,UAAU,CAAC,CAAC,GAAGC,QAAQ,CAACvD,OAAO,CAACG,QAAQ,CAAC,CAAC;IACnF,MAAMqD,GAAG,GAAG,IAAItF,IAAI,CAAC,CAAC;IAEtB,OAAOsF,GAAG,IAAIL,gBAAgB,IAAIK,GAAG,GAAGJ,cAAc;EACxD,CAAC;;EAED;EACA,MAAMK,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACvG,eAAe,EAAE;IAEtB,IAAI;MACFS,oBAAoB,CAAC,IAAI,CAAC;MAC1B,MAAM;QAAE6B;MAAK,CAAC,GAAG,MAAMvE,KAAK,CAACyI,GAAG,CAAC,aAAaxG,eAAe,CAACyG,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE;QAC7EjE,OAAO,EAAE;UACP,eAAe,EAAE,UAAUlD,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAIgD,IAAI,CAACG,OAAO,EAAE;QAChB;QACA9C,WAAW,CAAC+G,YAAY,IACtBA,YAAY,CAAC7D,GAAG,CAACC,OAAO,IACtBA,OAAO,CAAC2D,EAAE,KAAKzG,eAAe,CAACyG,EAAE,GAC7B;UAAE,GAAG3D,OAAO;UAAEgD,MAAM,EAAE;QAAY,CAAC,GACnChD,OACN,CACF,CAAC;QACDuC,KAAK,CAACjG,CAAC,CAAC,wBAAwB,CAAC,CAAC;MACpC,CAAC,MAAM;QACLiG,KAAK,CAAC/C,IAAI,CAACiB,OAAO,IAAInE,CAAC,CAAC,sBAAsB,CAAC,CAAC;MAClD;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAA6G,gBAAA,EAAAC,qBAAA;MACdlE,OAAO,CAAC5C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDuF,KAAK,CAAC,EAAAsB,gBAAA,GAAA7G,KAAK,CAAC4D,QAAQ,cAAAiD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrE,IAAI,cAAAsE,qBAAA,uBAApBA,qBAAA,CAAsBrD,OAAO,KAAInE,CAAC,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC,SAAS;MACRqB,oBAAoB,CAAC,KAAK,CAAC;MAC3BF,mBAAmB,CAAC,KAAK,CAAC;MAC1BJ,oBAAoB,CAAC,KAAK,CAAC;MAC3BF,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAM4G,cAAc,GAAIf,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,OAAO;MAChB,KAAK,gBAAgB;QACnB,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,MAAM;MACf;QACE,OAAO,SAAS;IACpB;EACF,CAAC;;EAED;EACA,MAAMgB,aAAa,GAAIhB,MAAM,IAAK;IAChC,OAAO1G,CAAC,CAAC,yBAAyB0G,MAAM,EAAE,EAAE;MAC1CiB,YAAY,EAAEjB,MAAM,CAACkB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnB,MAAM,CAACoB,KAAK,CAAC,CAAC;IAC/D,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIxB,QAAQ,IAAK;IACtC,IAAI,CAACvF,cAAc,IAAI,CAACA,cAAc,CAACgD,QAAQ,EAAE;MAC/C,OAAOpF,MAAM,CAAC,IAAIgD,IAAI,CAAC2E,QAAQ,CAAC,EAAE,KAAK,EAAE;QAAEyB,MAAM,EAAE5H,KAAK,GAAGnB,EAAE,GAAGC;MAAK,CAAC,CAAC;IACzE;IAEA,MAAM+I,aAAa,GAAG1I,2BAA2B,CAACgH,QAAQ,EAAEvF,cAAc,CAACgD,QAAQ,EAAE,YAAY,CAAC;IAClG,OAAOtE,MAAM,CAACuI,aAAa,EAAE,YAAY,CAAC,CAACrJ,MAAM,CAAC,cAAc,CAAC;EACnE,CAAC;;EAED;EACA,MAAMsJ,iBAAiB,GAAI3B,QAAQ,IAAK;IACtC,IAAI,CAACvF,cAAc,IAAI,CAACA,cAAc,CAACgD,QAAQ,EAAE;MAC/C,OAAOpF,MAAM,CAAC,IAAIgD,IAAI,CAAC2E,QAAQ,CAAC,EAAE,GAAG,EAAE;QAAEyB,MAAM,EAAE5H,KAAK,GAAGnB,EAAE,GAAGC;MAAK,CAAC,CAAC;IACvE;IAEA,MAAMiJ,iBAAiB,GAAG5I,2BAA2B,CAACgH,QAAQ,EAAEvF,cAAc,CAACgD,QAAQ,EAAE,qBAAqB,CAAC;IAC/G,OAAOtE,MAAM,CAACyI,iBAAiB,EAAE,qBAAqB,CAAC,CAACvJ,MAAM,CAAC,QAAQ,CAAC;EAC1E,CAAC;;EAED;EACA,MAAMwJ,oBAAoB,GAAGA,CAACC,cAAc,EAAExE,QAAQ,KAAK;IACzD,MAAMyE,WAAW,GAAGrB,QAAQ,CAACpD,QAAQ,EAAE,EAAE,CAAC;IAC1C,OAAOyE,WAAW,KAAK,EAAE,GAAGD,cAAc,GAAGA,cAAc,GAAG,CAAC;EACjE,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC3H,eAAe,EAAE,OAAO,IAAI;IAEjC,MAAM4H,WAAW,GAAGJ,oBAAoB,CAACxH,eAAe,CAAC+C,gBAAgB,EAAE/C,eAAe,CAACiD,QAAQ,CAAC;IAEpG,oBACEhE,OAAA,CAAC/C,MAAM;MACL2L,IAAI,EAAE3H,iBAAkB;MACxB4H,OAAO,EAAEA,CAAA,KAAM3H,oBAAoB,CAAC,KAAK,CAAE;MAC3C4H,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAC,QAAA,gBAEThJ,OAAA,CAAC9C,WAAW;QAAC+L,EAAE,EAAE;UAAEC,OAAO,EAAE,cAAc;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAH,QAAA,eAC3DhJ,OAAA,CAACnD,GAAG;UAACoM,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACzDhJ,OAAA,CAACnC,YAAY;YAAA0L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChB1J,OAAA,CAACpD,UAAU;YAAC+M,OAAO,EAAC,IAAI;YAAAX,QAAA,EACrB7I,CAAC,CAAC,yBAAyB;UAAC;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd1J,OAAA,CAAC7C,aAAa;QAAC8L,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAZ,QAAA,eAC3BhJ,OAAA,CAACxC,IAAI;UAACqM,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACzBhJ,OAAA,CAACxC,IAAI;YAACuM,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAChBhJ,OAAA,CAACnD,GAAG;cAACoM,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDhJ,OAAA,CAAC1C,MAAM;gBACL4M,GAAG,EAAEnJ,eAAe,CAACoJ,eAAgB;gBACrCC,GAAG,EAAErJ,eAAe,CAACsJ,YAAa;gBAClCpB,EAAE,EAAE;kBAAEqB,EAAE,EAAE,CAAC;kBAAEC,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAG;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACF1J,OAAA,CAACnD,GAAG;gBAAAmM,QAAA,gBACFhJ,OAAA,CAACpD,UAAU;kBAAC+M,OAAO,EAAC,IAAI;kBAAAX,QAAA,EACrBjI,eAAe,CAACsJ;gBAAY;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACb1J,OAAA,CAACpD,UAAU;kBAAC+M,OAAO,EAAC,OAAO;kBAACR,KAAK,EAAC,gBAAgB;kBAAAH,QAAA,EAC/CjI,eAAe,CAAC0J;gBAAa;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEP1J,OAAA,CAACxC,IAAI;YAACuM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACU,EAAE,EAAE,CAAE;YAAA1B,QAAA,gBACvBhJ,OAAA,CAACnD,GAAG;cAACoM,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDhJ,OAAA,CAACnC,YAAY;gBAACoL,EAAE,EAAE;kBAAEqB,EAAE,EAAE,CAAC;kBAAEnB,KAAK,EAAE;gBAAe;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtD1J,OAAA,CAACpD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACR,KAAK,EAAC,gBAAgB;gBAAAH,QAAA,EACnD7I,CAAC,CAAC,eAAe;cAAC;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN1J,OAAA,CAACpD,UAAU;cAAC+M,OAAO,EAAC,OAAO;cAAAX,QAAA,EACxBd,iBAAiB,CAACnH,eAAe,CAAC2F,QAAQ;YAAC;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEP1J,OAAA,CAACxC,IAAI;YAACuM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACU,EAAE,EAAE,CAAE;YAAA1B,QAAA,gBACvBhJ,OAAA,CAACnD,GAAG;cAACoM,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDhJ,OAAA,CAACjC,QAAQ;gBAACkL,EAAE,EAAE;kBAAEqB,EAAE,EAAE,CAAC;kBAAEnB,KAAK,EAAE;gBAAe;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClD1J,OAAA,CAACpD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACR,KAAK,EAAC,gBAAgB;gBAAAH,QAAA,EACnD7I,CAAC,CAAC,eAAe;cAAC;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN1J,OAAA,CAACpD,UAAU;cAAC+M,OAAO,EAAC,OAAO;cAAAX,QAAA,EACxBX,iBAAiB,CAACtH,eAAe,CAAC2F,QAAQ;YAAC;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEP1J,OAAA,CAACxC,IAAI;YAACuM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACU,EAAE,EAAE,CAAE;YAAA1B,QAAA,gBACvBhJ,OAAA,CAACnD,GAAG;cAACoM,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDhJ,OAAA,CAACjC,QAAQ;gBAACkL,EAAE,EAAE;kBAAEqB,EAAE,EAAE,CAAC;kBAAEnB,KAAK,EAAE;gBAAe;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClD1J,OAAA,CAACpD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACR,KAAK,EAAC,gBAAgB;gBAAAH,QAAA,EACnD7I,CAAC,CAAC,mBAAmB;cAAC;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN1J,OAAA,CAACpD,UAAU;cAAC+M,OAAO,EAAC,OAAO;cAAAX,QAAA,GACxBjI,eAAe,CAACiD,QAAQ,EAAC,GAAC,EAAC7D,CAAC,CAAC,kBAAkB,CAAC;YAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEP1J,OAAA,CAACxC,IAAI;YAACuM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACU,EAAE,EAAE,CAAE;YAAA1B,QAAA,gBACvBhJ,OAAA,CAACnD,GAAG;cAACoM,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDhJ,OAAA,CAAC7B,SAAS;gBAAC8K,EAAE,EAAE;kBAAEqB,EAAE,EAAE,CAAC;kBAAEnB,KAAK,EAAE;gBAAe;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnD1J,OAAA,CAACpD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACR,KAAK,EAAC,gBAAgB;gBAAAH,QAAA,EACnD7I,CAAC,CAAC,gBAAgB;cAAC;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN1J,OAAA,CAACpD,UAAU;cAAC+M,OAAO,EAAC,OAAO;cAAAX,QAAA,GAAC,GACzB,EAACL,WAAW,CAACgC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEP1J,OAAA,CAACxC,IAAI;YAACuM,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,gBAChBhJ,OAAA,CAACnD,GAAG;cAACoM,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDhJ,OAAA,CAAC/B,UAAU;gBAACgL,EAAE,EAAE;kBAAEqB,EAAE,EAAE,CAAC;kBAAEnB,KAAK,EAAE;gBAAe;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpD1J,OAAA,CAACpD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACR,KAAK,EAAC,gBAAgB;gBAAAH,QAAA,EACnD7I,CAAC,CAAC,iBAAiB;cAAC;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN1J,OAAA,CAACzC,IAAI;cACHqN,KAAK,EAAE/C,aAAa,CAAC9G,eAAe,CAAC8F,MAAM,CAAE;cAC7CsC,KAAK,EAAEvB,cAAc,CAAC7G,eAAe,CAAC8F,MAAM,CAAE;cAC9C8C,OAAO,EAAC;YAAQ;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB1J,OAAA,CAAC5C,aAAa;QAAA4L,QAAA,gBACZhJ,OAAA,CAAC3C,MAAM;UAACwN,OAAO,EAAEA,CAAA,KAAM3J,oBAAoB,CAAC,KAAK,CAAE;UAAA8H,QAAA,EAChD7I,CAAC,CAAC,cAAc;QAAC;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,EAGR3I,eAAe,iBACdf,OAAA,CAAC3C,MAAM;UACLwN,OAAO,EAAEA,CAAA,KAAM/D,cAAc,CAAC/F,eAAe,CAAC,IAAIwF,iBAAiB,CAACxF,eAAe,CAAE;UACrFoI,KAAK,EAAErC,cAAc,CAAC/F,eAAe,CAAC,GAAG,SAAS,GAAG,SAAU;UAC/D4I,OAAO,EAAE7C,cAAc,CAAC/F,eAAe,CAAC,GAAG,WAAW,GAAG,UAAW;UACpE+J,SAAS,eAAE9K,OAAA,CAACvB,aAAa;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BqB,QAAQ,EAAE,CAACjE,cAAc,CAAC/F,eAAe,CAAE;UAC3CkI,EAAE,EAAE;YACFqB,EAAE,EAAE,CAAC;YACL,IAAIxD,cAAc,CAAC/F,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG;cACzCoI,KAAK,EAAE7I,KAAK,CAAC0K,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;cAC9BC,WAAW,EAAE5K,KAAK,CAAC0K,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;cACpCE,eAAe,EAAE7K,KAAK,CAAC0K,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;cACxC,SAAS,EAAE;gBACTE,eAAe,EAAE7K,KAAK,CAAC0K,OAAO,CAACC,IAAI,CAAC,GAAG;cACzC;YACF,CAAC;UACH,CAAE;UAAAjC,QAAA,EAEDlC,cAAc,CAAC/F,eAAe,CAAC,GAAGZ,CAAC,CAAC,eAAe,CAAC,GAAGA,CAAC,CAAC,qBAAqB;QAAC;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CACT,EAEA,CAAA3I,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8F,MAAM,MAAK,WAAW,iBACtC7G,OAAA,CAAC3C,MAAM;UACLwN,OAAO,EAAEA,CAAA,KAAM;YACb3J,oBAAoB,CAAC,KAAK,CAAC;YAC3BI,mBAAmB,CAAC,IAAI,CAAC;UAC3B,CAAE;UACF6H,KAAK,EAAC,OAAO;UACbQ,OAAO,EAAC,WAAW;UACnBmB,SAAS,eAAE9K,OAAA,CAACrB,UAAU;YAAA4K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAV,QAAA,EAEzB7I,CAAC,CAAC,iBAAiB;QAAC;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAM0B,kBAAkB,GAAGA,CAAA,kBACzBpL,OAAA,CAAC/C,MAAM;IAAC2L,IAAI,EAAEvH,gBAAiB;IAACwH,OAAO,EAAEA,CAAA,KAAMvH,mBAAmB,CAAC,KAAK,CAAE;IAAA0H,QAAA,gBACxEhJ,OAAA,CAAC9C,WAAW;MAAA8L,QAAA,GACT7I,CAAC,CAAC,wBAAwB,CAAC,eAC5BH,OAAA,CAACtC,UAAU;QACT,cAAW,OAAO;QAClBmN,OAAO,EAAEA,CAAA,KAAMvJ,mBAAmB,CAAC,KAAK,CAAE;QAC1C2H,EAAE,EAAE;UAAEoC,QAAQ,EAAE,UAAU;UAAEC,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,eAE/ChJ,OAAA,CAACnB,SAAS;UAAA0K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACd1J,OAAA,CAAC7C,aAAa;MAAA6L,QAAA,gBACZhJ,OAAA,CAACpD,UAAU;QAAC+M,OAAO,EAAC,OAAO;QAAAX,QAAA,EACxB7I,CAAC,CAAC,wBAAwB;MAAC;QAAAoJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,EACZ3I,eAAe,iBACdf,OAAA,CAACnD,GAAG;QAACoM,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAZ,QAAA,gBACjBhJ,OAAA,CAACpD,UAAU;UAAC+M,OAAO,EAAC,OAAO;UAAC6B,YAAY;UAAAxC,QAAA,gBACtChJ,OAAA;YAAAgJ,QAAA,GAAS7I,CAAC,CAAC,kBAAkB,CAAC,EAAC,GAAC;UAAA;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC3I,eAAe,CAACsJ,YAAY;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACb1J,OAAA,CAACpD,UAAU;UAAC+M,OAAO,EAAC,OAAO;UAAC6B,YAAY;UAAAxC,QAAA,gBACtChJ,OAAA;YAAAgJ,QAAA,GAAS7I,CAAC,CAAC,eAAe,CAAC,EAAC,GAAC;UAAA;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxB,iBAAiB,CAACnH,eAAe,CAAC2F,QAAQ,CAAC;QAAA;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACb1J,OAAA,CAACpD,UAAU;UAAC+M,OAAO,EAAC,OAAO;UAAAX,QAAA,gBACzBhJ,OAAA;YAAAgJ,QAAA,GAAS7I,CAAC,CAAC,eAAe,CAAC,EAAC,GAAC;UAAA;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACrB,iBAAiB,CAACtH,eAAe,CAAC2F,QAAQ,CAAC;QAAA;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAChB1J,OAAA,CAAC5C,aAAa;MAAA4L,QAAA,gBACZhJ,OAAA,CAAC3C,MAAM;QAACwN,OAAO,EAAEA,CAAA,KAAMvJ,mBAAmB,CAAC,KAAK,CAAE;QAAA0H,QAAA,EAC/C7I,CAAC,CAAC,eAAe;MAAC;QAAAoJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACT1J,OAAA,CAAC3C,MAAM;QACLwN,OAAO,EAAEvD,mBAAoB;QAC7B6B,KAAK,EAAC,OAAO;QACbQ,OAAO,EAAC,WAAW;QACnBoB,QAAQ,EAAExJ,iBAAkB;QAAAyH,QAAA,EAE3BzH,iBAAiB,GAAGpB,CAAC,CAAC,qBAAqB,CAAC,GAAGA,CAAC,CAAC,8BAA8B;MAAC;QAAAoJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACE1J,OAAA,CAACT,MAAM;IAAAyJ,QAAA,gBACLhJ,OAAA,CAACrD,SAAS;MAACmM,QAAQ,EAAC,IAAI;MAACG,EAAE,EAAE;QAAEwC,EAAE,EAAE;MAAE,CAAE;MAAAzC,QAAA,gBACrChJ,OAAA,CAAClD,KAAK;QAAC4O,SAAS,EAAE,CAAE;QAACzC,EAAE,EAAE;UAAE0C,CAAC,EAAE,CAAC;UAAE1B,EAAE,EAAE,CAAC;UAAEf,OAAO,EAAE,cAAc;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAH,QAAA,eAChFhJ,OAAA,CAACnD,GAAG;UAACoM,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEwC,cAAc,EAAE,eAAe;YAAEvC,UAAU,EAAE,QAAQ;YAAEwC,QAAQ,EAAE,MAAM;YAAEvC,GAAG,EAAE;UAAE,CAAE;UAAAN,QAAA,gBAC5GhJ,OAAA,CAACnD,GAAG;YAAAmM,QAAA,gBACFhJ,OAAA,CAACpD,UAAU;cAAC+M,OAAO,EAAC,IAAI;cAAC6B,YAAY;cAACvC,EAAE,EAAE;gBAAE6C,UAAU,EAAE;cAAO,CAAE;cAAA9C,QAAA,EAC9D7I,CAAC,CAAC,wBAAwB;YAAC;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACb1J,OAAA,CAACpD,UAAU;cAAC+M,OAAO,EAAC,OAAO;cAACV,EAAE,EAAE;gBAAE8C,OAAO,EAAE;cAAI,CAAE;cAAA/C,QAAA,EAC9C7I,CAAC,CAAC,mCAAmC;YAAC;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN1J,OAAA,CAACnD,GAAG;YAACoM,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACzDhJ,OAAA,CAACnD,GAAG;cAACoM,EAAE,EAAE;gBAAE+C,SAAS,EAAE;cAAQ,CAAE;cAAAhD,QAAA,gBAC9BhJ,OAAA,CAACpD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAACV,EAAE,EAAE;kBAAE8C,OAAO,EAAE,GAAG;kBAAE9B,EAAE,EAAE;gBAAI,CAAE;gBAAAjB,QAAA,EACvD7I,CAAC,CAAC,wBAAwB;cAAC;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACb1J,OAAA,CAACpD,UAAU;gBAAC+M,OAAO,EAAC,IAAI;gBAACV,EAAE,EAAE;kBAAE6C,UAAU,EAAE;gBAAO,CAAE;gBAAA9C,QAAA,GAAC,eAChD,EAACjK,MAAM,CAACqD,gBAAgB,EAAE,OAAO,EAAE;kBAAE+F,MAAM,EAAE5H,KAAK,GAAGnB,EAAE,GAAGC;gBAAK,CAAC,CAAC,EAAC,KAAG,EAACN,MAAM,CAACE,OAAO,CAACmD,gBAAgB,EAAE,CAAC,CAAC,EAAE,aAAa,EAAE;kBAAE+F,MAAM,EAAE5H,KAAK,GAAGnB,EAAE,GAAGC;gBAAK,CAAC,CAAC;cAAA;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN1J,OAAA,CAACnD,GAAG;cAACoM,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEE,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACnChJ,OAAA,CAACrC,OAAO;gBAACsO,KAAK,EAAE9L,CAAC,CAAC,sBAAsB,CAAE;gBAAA6I,QAAA,eACxChJ,OAAA;kBAAAgJ,QAAA,eACEhJ,OAAA,CAACtC,UAAU;oBACTmN,OAAO,EAAEpI,gBAAiB;oBAC1BsI,QAAQ,EAAEhI,sBAAsB,CAAC,CAAE;oBACnCkG,EAAE,EAAE;sBACFE,KAAK,EAAE,OAAO;sBACdD,OAAO,EAAE,0BAA0B;sBACnC,SAAS,EAAE;wBACTA,OAAO,EAAE;sBACX,CAAC;sBACD,YAAY,EAAE;wBACZC,KAAK,EAAE,0BAA0B;wBACjCD,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAAF,QAAA,eAEFhJ,OAAA,CAAC3B,eAAe;sBAAAkL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACV1J,OAAA,CAACrC,OAAO;gBAACsO,KAAK,EAAE9L,CAAC,CAAC,kBAAkB,CAAE;gBAAA6I,QAAA,eACpChJ,OAAA;kBAAAgJ,QAAA,eACEhJ,OAAA,CAACtC,UAAU;oBACTmN,OAAO,EAAElI,YAAa;oBACtBoI,QAAQ,EAAE/H,kBAAkB,CAAC,CAAE;oBAC/BiG,EAAE,EAAE;sBACFE,KAAK,EAAE,OAAO;sBACdD,OAAO,EAAE,0BAA0B;sBACnC,SAAS,EAAE;wBACTA,OAAO,EAAE;sBACX,CAAC;sBACD,YAAY,EAAE;wBACZC,KAAK,EAAE,0BAA0B;wBACjCD,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAAF,QAAA,eAEFhJ,OAAA,CAACzB,gBAAgB;sBAAAgL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGR1J,OAAA,CAAClD,KAAK;QAAC4O,SAAS,EAAE,CAAE;QAACzC,EAAE,EAAE;UAAE0C,CAAC,EAAE,CAAC;UAAE1B,EAAE,EAAE,CAAC;UAAEf,OAAO,EAAE;QAAmB,CAAE;QAAAF,QAAA,eACpEhJ,OAAA,CAACnD,GAAG;UAACoM,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACzDhJ,OAAA,CAACnD,GAAG;YAACoM,EAAE,EAAE;cACPC,OAAO,EAAE,cAAc;cACvBC,KAAK,EAAE,OAAO;cACdwC,CAAC,EAAE,CAAC;cACJO,YAAY,EAAE,CAAC;cACfC,QAAQ,EAAE,EAAE;cACZH,SAAS,EAAE;YACb,CAAE;YAAAhD,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN1J,OAAA,CAACnD,GAAG;YAAAmM,QAAA,gBACFhJ,OAAA,CAACpD,UAAU;cAAC+M,OAAO,EAAC,OAAO;cAACR,KAAK,EAAC,gBAAgB;cAACF,EAAE,EAAE;gBAAEgB,EAAE,EAAE;cAAI,CAAE;cAAAjB,QAAA,EAChE7I,CAAC,CAAC,sBAAsB;YAAC;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACb1J,OAAA,CAACpD,UAAU;cAAC+M,OAAO,EAAC,IAAI;cAACV,EAAE,EAAE;gBAAE6C,UAAU,EAAE;cAAO,CAAE;cAAA9C,QAAA,EACjD7H,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEgD,QAAQ,GACvBtE,MAAM,CAAC,CAAC,CAACuM,GAAG,CAAC,CAAC,CAACC,GAAG,CAACzM,mBAAmB,CAACuB,cAAc,CAACgD,QAAQ,CAAC,EAAE,SAAS,CAAC,CAACpF,MAAM,CAAC,gCAAgC,CAAC,GAEpHA,MAAM,CAAC,IAAIgD,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE;gBACzBoG,MAAM,EAAE5H,KAAK,GAAGnB,EAAE,GAAGC;cACvB,CAAC;YACF;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,EACZ,CAAAvI,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgD,QAAQ,kBACvBnE,OAAA,CAACpD,UAAU;cAAC+M,OAAO,EAAC,SAAS;cAACV,EAAE,EAAE;gBAAE8C,OAAO,EAAE;cAAI,CAAE;cAAA/C,QAAA,EAChD7H,cAAc,CAACgD;YAAQ;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEP/I,OAAO,gBACNX,OAAA,CAACnD,GAAG;QAACoM,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEwC,cAAc,EAAE,QAAQ;UAAEH,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,eAC5DhJ,OAAA,CAACjD,gBAAgB;UAAAwM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACJ7I,KAAK,gBACPb,OAAA,CAAChD,KAAK;QAACsP,QAAQ,EAAC,OAAO;QAACrD,EAAE,EAAE;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,EACnCnI;MAAK;QAAA0I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAER1J,OAAA,CAACR,mBAAmB;QAClBiB,QAAQ,EAAEA,QAAS;QACnBE,OAAO,EAAEA,OAAQ;QACjByB,gBAAgB,EAAEA,gBAAiB;QACnCI,UAAU,EAAEA,UAAW;QACvB+J,aAAa,EAAElG,iBAAkB;QACjCmG,eAAe,EAAElG,wBAAyB,CAAC;QAAA;QAC3CmG,cAAc,EAAEtL,cAAe,CAAC;QAAA;QAChCkH,iBAAiB,EAAEA,iBAAkB;QACrCT,cAAc,EAAEA,cAAe;QAC/BC,aAAa,EAAEA,aAAc;QAC7B6E,aAAa,EAAE,IAAK,CAAC;QAAA;QACrB1K,cAAc,EAAEA,cAAe,CAAC;QAAA;QAChC2K,WAAW,EAAEpH,eAAgB,CAAC;QAAA;QAC9BrD,YAAY,EAAEA,YAAa,CAAC;MAAA;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF,EAEAhB,mBAAmB,CAAC,CAAC,EACrB0C,kBAAkB,CAAC,CAAC;IAAA;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAGZ1J,OAAA,CAAC/C,MAAM;MACL2P,UAAU;MACVhE,IAAI,EAAEnH,WAAY;MAClBoH,OAAO,EAAElC,kBAAmB;MAAAqC,QAAA,EAE3BrH,cAAc,iBACb3B,OAAA,CAACF,eAAe;QACd+M,MAAM,EAAElL,cAAc,CAAC6E,SAAU;QACjCsG,SAAS,EAAEnL,cAAc,CAAC8E,UAAW;QACrCsG,WAAW,EAAEpL,cAAe;QAC5BkH,OAAO,EAAElC;MAAmB;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACxJ,EAAA,CA5sBID,eAAe;EAAA,QACCvD,cAAc,EAChB4C,OAAO,EACX7B,QAAQ;AAAA;AAAAuP,EAAA,GAHlB/M,eAAe;AA8sBrB,eAAeA,eAAe;AAAC,IAAA+M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}